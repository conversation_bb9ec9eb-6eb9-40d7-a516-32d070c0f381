@extends('layouts.app')

@section('title', 'COBIT 2019 - Évaluation')

@section('content')
<div class="fade-in">
    <!-- Header avec navigation par onglets -->
    <div class="card mb-6">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Évaluation COBIT 2019</h1>
                    <p class="text-gray-600 mt-1">Design Factors - Facteurs de conception</p>
                </div>
                <div class="flex space-x-3">
                    <button onclick="saveAllData()" class="btn btn-success">
                        <i class="fas fa-save mr-2"></i>Sauvegarder Tout
                    </button>
                    <button onclick="exportData()" class="btn btn-secondary">
                        <i class="fas fa-download mr-2"></i>Exporter
                    </button>
                    <button onclick="resetAllData()" class="btn btn-danger">
                        <i class="fas fa-redo mr-2"></i>Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Onglets des Design Factors -->
        <div class="p-4 border-b">
            <div class="flex flex-wrap gap-2">
                @foreach($designFactors as $df)
                <button
                    onclick="switchToDF({{ $df->getNumberFromCode() }})"
                    id="tab-df{{ $df->getNumberFromCode() }}"
                    class="df-tab px-4 py-2 rounded-lg font-medium transition-all {{ $df->getNumberFromCode() == 1 ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200' }}">
                    {{ $df->code }}
                    <span class="ml-2 text-xs opacity-75">{{ $df->title }}</span>
                </button>
                @endforeach

                <!-- Onglet Résultats -->
                <button
                    onclick="switchToResults()"
                    id="tab-results"
                    class="df-tab px-4 py-2 rounded-lg font-medium transition-all bg-green-100 text-green-700 hover:bg-green-200">
                    <i class="fas fa-chart-bar mr-2"></i>Résultats
                </button>
            </div>
        </div>

        <!-- Progress Bar -->
        <div class="p-4">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Progression globale</span>
                <span class="text-sm text-gray-600" id="progress-text">0/10 complétés</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" id="progress-bar" style="width: 0%"></div>
            </div>
        </div>
    </div>

    <!-- Contenu principal - Design Factors -->
    <div id="evaluationContainer">
        @foreach($designFactors as $df)
        <div id="df{{ $df->getNumberFromCode() }}-content" class="df-content card {{ $df->getNumberFromCode() != 1 ? 'hidden' : '' }}">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">{{ $df->title }}</h2>
                        <p class="text-gray-600 mt-1">{{ $df->description }}</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="text-sm text-gray-500">{{ $df->code }}</span>
                        <button onclick="resetDF({{ $df->getNumberFromCode() }})" class="btn btn-secondary btn-sm">
                            <i class="fas fa-undo mr-1"></i>Reset
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Section des inputs -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-sliders-h mr-2 text-blue-600"></i>
                            Paramètres d'évaluation
                        </h3>

                        <div class="space-y-6">
                            @foreach($df->labels as $index => $label)
                            @php
                                $inputType = $df->metadata['type'] ?? 'slider';
                                $defaultValue = $df->defaults[$index] ?? 1;
                                $min = $df->metadata['min'] ?? 0;
                                $max = $df->metadata['max'] ?? 5;
                                $step = $df->metadata['step'] ?? 1;
                            @endphp

                            <div class="input-group" data-df="{{ $df->getNumberFromCode() }}" data-index="{{ $index }}">
                                <div class="flex justify-between items-center mb-2">
                                    <label class="block text-sm font-medium text-gray-700">{{ $label }}</label>
                                    <span class="text-sm font-bold text-blue-600 value-display" id="value-df{{ $df->getNumberFromCode() }}-{{ $index }}">{{ $defaultValue }}</span>
                                </div>

                                @if($inputType === 'slider')
                                <input
                                    type="range"
                                    min="{{ $min }}"
                                    max="{{ $max }}"
                                    step="{{ $step }}"
                                    value="{{ $defaultValue }}"
                                    class="slider input-control"
                                    id="input-df{{ $df->getNumberFromCode() }}-{{ $index }}"
                                    data-df="{{ $df->getNumberFromCode() }}"
                                    data-index="{{ $index }}"
                                    onchange="updateDFValue({{ $df->getNumberFromCode() }}, {{ $index }}, this.value)"
                                >
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>{{ $min }}</span>
                                    <span>{{ round(($min + $max) / 2, 1) }}</span>
                                    <span>{{ $max }}</span>
                                </div>

                                @elseif($inputType === 'dropdown')
                                <select
                                    class="w-full p-2 border border-gray-300 rounded-lg input-control"
                                    id="input-df{{ $df->getNumberFromCode() }}-{{ $index }}"
                                    data-df="{{ $df->getNumberFromCode() }}"
                                    data-index="{{ $index }}"
                                    onchange="updateDFValue({{ $df->getNumberFromCode() }}, {{ $index }}, this.value)"
                                >
                                    @foreach($df->metadata['options'] ?? [] as $option)
                                    <option value="{{ $option['value'] }}" {{ $option['value'] == $defaultValue ? 'selected' : '' }}>
                                        {{ $option['label'] }}
                                    </option>
                                    @endforeach
                                </select>

                                @elseif($inputType === 'checkbox')
                                <div class="flex items-center space-x-3">
                                    <input
                                        type="checkbox"
                                        class="w-4 h-4 text-blue-600 input-control"
                                        id="input-df{{ $df->getNumberFromCode() }}-{{ $index }}"
                                        data-df="{{ $df->getNumberFromCode() }}"
                                        data-index="{{ $index }}"
                                        {{ $defaultValue > 0.5 ? 'checked' : '' }}
                                        onchange="updateDFValue({{ $df->getNumberFromCode() }}, {{ $index }}, this.checked ? 1 : 0)"
                                    >
                                    <label for="input-df{{ $df->getNumberFromCode() }}-{{ $index }}" class="text-sm text-gray-700">
                                        Activé
                                    </label>
                                </div>
                                @endif
                            </div>
                            @endforeach
                        </div>

                        <!-- Actions pour ce DF -->
                        <div class="mt-8 flex space-x-4">
                            <button onclick="saveDFData({{ $df->getNumberFromCode() }})" class="btn btn-success">
                                <i class="fas fa-save mr-2"></i>Sauvegarder {{ $df->code }}
                            </button>
                            <button onclick="resetDF({{ $df->getNumberFromCode() }})" class="btn btn-secondary">
                                <i class="fas fa-undo mr-2"></i>Réinitialiser
                            </button>
                        </div>
                    </div>

                    <!-- Section des graphiques -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-chart-line mr-2 text-green-600"></i>
                            Visualisation en temps réel
                        </h3>

                        <!-- Graphique Radar par Domaine -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h4 class="text-md font-semibold flex items-center">
                                    <i class="fas fa-chart-pie mr-2 text-blue-600"></i>
                                    Performance par Domaine
                                </h4>
                            </div>
                            <div class="p-4">
                                <div class="chart-container" style="height: 300px;">
                                    <canvas id="radarChart-df{{ $df->getNumberFromCode() }}"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Graphique en Barres - Top Objectifs -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h4 class="text-md font-semibold flex items-center">
                                    <i class="fas fa-chart-bar mr-2 text-purple-600"></i>
                                    Top 10 Objectifs Impactés
                                </h4>
                            </div>
                            <div class="p-4">
                                <div class="chart-container" style="height: 300px;">
                                    <canvas id="barChart-df{{ $df->getNumberFromCode() }}"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Statistiques en temps réel -->
                        <div class="card">
                            <div class="card-header">
                                <h4 class="text-md font-semibold flex items-center">
                                    <i class="fas fa-calculator mr-2 text-orange-600"></i>
                                    Statistiques {{ $df->code }}
                                </h4>
                            </div>
                            <div class="p-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                                        <div class="text-2xl font-bold text-blue-600" id="avg-score-df{{ $df->getNumberFromCode() }}">0.0</div>
                                        <div class="text-xs text-gray-600">Score Moyen</div>
                                    </div>
                                    <div class="text-center p-3 bg-green-50 rounded-lg">
                                        <div class="text-2xl font-bold text-green-600" id="max-impact-df{{ $df->getNumberFromCode() }}">0</div>
                                        <div class="text-xs text-gray-600">Impact Max</div>
                                    </div>
                                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                                        <div class="text-2xl font-bold text-purple-600" id="affected-objectives-df{{ $df->getNumberFromCode() }}">0</div>
                                        <div class="text-xs text-gray-600">Objectifs Affectés</div>
                                    </div>
                                    <div class="text-center p-3 bg-orange-50 rounded-lg">
                                        <div class="text-2xl font-bold text-orange-600" id="completion-df{{ $df->getNumberFromCode() }}">0%</div>
                                        <div class="text-xs text-gray-600">Complétude</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach

        <!-- Section Résultats Globaux -->
        <div id="results-content" class="df-content card hidden">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">
                            <i class="fas fa-chart-line mr-3 text-green-600"></i>
                            Résultats Globaux
                        </h2>
                        <p class="text-gray-600 mt-1">Analyse complète de tous les Design Factors</p>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="exportResults()" class="btn btn-primary">
                            <i class="fas fa-file-pdf mr-2"></i>Export PDF
                        </button>
                        <button onclick="exportJSON()" class="btn btn-secondary">
                            <i class="fas fa-download mr-2"></i>Export JSON
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <!-- Résumé des métriques -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="card">
                        <div class="p-6 text-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-list text-blue-600 text-xl"></i>
                            </div>
                            <div class="text-2xl font-bold text-gray-900" id="total-objectives">40</div>
                            <div class="text-sm text-gray-600">Objectifs Évalués</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="p-6 text-center">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                            <div class="text-2xl font-bold text-red-600" id="high-priority">0</div>
                            <div class="text-sm text-gray-600">Priorité Haute</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="p-6 text-center">
                            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-exclamation-circle text-yellow-600 text-xl"></i>
                            </div>
                            <div class="text-2xl font-bold text-yellow-600" id="medium-priority">0</div>
                            <div class="text-sm text-gray-600">Priorité Moyenne</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="p-6 text-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-check-circle text-green-600 text-xl"></i>
                            </div>
                            <div class="text-2xl font-bold text-green-600" id="low-priority">0</div>
                            <div class="text-sm text-gray-600">Priorité Faible</div>
                        </div>
                    </div>
                </div>

                <!-- Graphiques globaux -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Radar global -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-xl font-semibold text-gray-900">Performance Globale par Domaine</h3>
                        </div>
                        <div class="p-6">
                            <div class="chart-container">
                                <canvas id="globalRadarChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Analyse des gaps -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-xl font-semibold text-gray-900">Analyse des Écarts</h3>
                        </div>
                        <div class="p-6">
                            <div class="chart-container">
                                <canvas id="gapAnalysisChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tableau des résultats -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-xl font-semibold text-gray-900">Résultats Détaillés par Objectif</h3>
                        <div class="flex space-x-2">
                            <button onclick="sortResults('objective')" class="btn btn-sm btn-secondary">
                                <i class="fas fa-sort-alpha-down mr-1"></i>Trier par Objectif
                            </button>
                            <button onclick="sortResults('priority')" class="btn btn-sm btn-secondary">
                                <i class="fas fa-sort-amount-down mr-1"></i>Trier par Priorité
                            </button>
                            <button onclick="sortResults('gap')" class="btn btn-sm btn-secondary">
                                <i class="fas fa-sort-numeric-down mr-1"></i>Trier par Écart
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200" id="resultsTable">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Objectif</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Domaine</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Baseline</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Écart</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priorité</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200" id="resultsTableBody">
                                    <!-- Les résultats seront injectés ici par JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Configuration globale
    let currentDF = 1;
    let evaluationData = {};
    let charts = {};
    let designFactorsData = @json($designFactors);
    let designFactors = [];

    // Convertir les design factors en array si c'est un objet
    if (Array.isArray(designFactorsData)) {
        designFactors = designFactorsData;
    } else if (typeof designFactorsData === 'object') {
        designFactors = Object.values(designFactorsData);
    }

    console.log('Design Factors loaded:', designFactors);

    // Données COBIT 2019
    const cobitData = {
        objectives: [
            'EDM01', 'EDM02', 'EDM03', 'EDM04', 'EDM05',
            'APO01', 'APO02', 'APO03', 'APO04', 'APO05', 'APO06', 'APO07', 'APO08', 'APO09', 'APO10', 'APO11', 'APO12', 'APO13', 'APO14',
            'BAI01', 'BAI02', 'BAI03', 'BAI04', 'BAI05', 'BAI06', 'BAI07', 'BAI08', 'BAI09', 'BAI10', 'BAI11',
            'DSS01', 'DSS02', 'DSS03', 'DSS04', 'DSS05', 'DSS06',
            'MEA01', 'MEA02', 'MEA03', 'MEA04'
        ],
        domains: {
            'EDM': ['EDM01', 'EDM02', 'EDM03', 'EDM04', 'EDM05'],
            'APO': ['APO01', 'APO02', 'APO03', 'APO04', 'APO05', 'APO06', 'APO07', 'APO08', 'APO09', 'APO10', 'APO11', 'APO12', 'APO13', 'APO14'],
            'BAI': ['BAI01', 'BAI02', 'BAI03', 'BAI04', 'BAI05', 'BAI06', 'BAI07', 'BAI08', 'BAI09', 'BAI10', 'BAI11'],
            'DSS': ['DSS01', 'DSS02', 'DSS03', 'DSS04', 'DSS05', 'DSS06'],
            'MEA': ['MEA01', 'MEA02', 'MEA03', 'MEA04']
        }
    };

    // Matrices de calcul COBIT (simplifiées pour la démo)
    const cobitMatrices = {
        DF1: generateMatrix(40, 4, 0.1, 0.3),
        DF2: generateMatrix(40, 4, 0.05, 0.25),
        DF3: generateMatrix(40, 4, 0.08, 0.28),
        DF4: generateMatrix(40, 4, 0.12, 0.35),
        DF5: generateMatrix(40, 2, 0.15, 0.4),
        DF6: generateMatrix(40, 3, 0.1, 0.3),
        DF7: generateMatrix(40, 3, 0.08, 0.25),
        DF8: generateMatrix(40, 2, 0.06, 0.2),
        DF9: generateMatrix(40, 3, 0.1, 0.3),
        DF10: generateMatrix(40, 3, 0.05, 0.2)
    };

    // Initialisation
    document.addEventListener('DOMContentLoaded', function() {
        console.log('COBIT Evaluation App initialized');
        initializeEvaluationData();
        updateAllCharts();
        updateProgress();
    });

    // Générer une matrice de calcul
    function generateMatrix(objectives, inputs, minWeight, maxWeight) {
        const matrix = [];
        for (let i = 0; i < objectives; i++) {
            const row = [];
            for (let j = 0; j < inputs; j++) {
                row.push(Math.random() * (maxWeight - minWeight) + minWeight);
            }
            matrix.push(row);
        }
        return matrix;
    }

    // Initialiser les données d'évaluation
    function initializeEvaluationData() {
        console.log('Initializing evaluation data...');
        console.log('designFactorsData:', designFactorsData);

        // Convertir les design factors en array si nécessaire
        if (Array.isArray(designFactorsData)) {
            designFactors = designFactorsData;
        } else if (typeof designFactorsData === 'object') {
            designFactors = Object.values(designFactorsData);
        }

        console.log('Processed designFactors:', designFactors);

        if (!Array.isArray(designFactors)) {
            console.error('designFactors is not an array:', designFactors);
            return;
        }

        designFactors.forEach(df => {
            if (!df || !df.code) {
                console.error('Invalid DF:', df);
                return;
            }

            const dfNumber = df.code.replace('DF', '');
            const defaults = df.defaults || [1, 1, 1, 1];

            evaluationData[`DF${dfNumber}`] = {
                inputs: [...defaults],
                scores: new Array(cobitData.objectives.length).fill(0),
                baselines: new Array(cobitData.objectives.length).fill(2.5)
            };

            console.log(`Initialized DF${dfNumber}:`, evaluationData[`DF${dfNumber}`]);
        });

        console.log('Final evaluationData:', evaluationData);
    }

    // Navigation entre les DF
    function switchToDF(dfNumber) {
        // Masquer tous les contenus
        document.querySelectorAll('.df-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Afficher le contenu du DF sélectionné
        document.getElementById(`df${dfNumber}-content`).classList.remove('hidden');

        // Mettre à jour les onglets
        document.querySelectorAll('.df-tab').forEach(tab => {
            tab.classList.remove('bg-blue-600', 'text-white');
            tab.classList.add('bg-gray-100', 'text-gray-700');
        });

        document.getElementById(`tab-df${dfNumber}`).classList.remove('bg-gray-100', 'text-gray-700');
        document.getElementById(`tab-df${dfNumber}`).classList.add('bg-blue-600', 'text-white');

        currentDF = dfNumber;
        updateDFCharts(dfNumber);
    }

    // Basculer vers les résultats
    function switchToResults() {
        // Masquer tous les contenus
        document.querySelectorAll('.df-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Afficher les résultats
        document.getElementById('results-content').classList.remove('hidden');

        // Mettre à jour les onglets
        document.querySelectorAll('.df-tab').forEach(tab => {
            tab.classList.remove('bg-blue-600', 'text-white', 'bg-green-600');
            tab.classList.add('bg-gray-100', 'text-gray-700');
        });

        document.getElementById('tab-results').classList.remove('bg-gray-100', 'text-gray-700', 'bg-green-100', 'text-green-700');
        document.getElementById('tab-results').classList.add('bg-green-600', 'text-white');

        calculateGlobalResults();
    }

    // Mettre à jour une valeur de DF
    function updateDFValue(dfNumber, index, value) {
        const numValue = parseFloat(value);
        evaluationData[`DF${dfNumber}`].inputs[index] = numValue;

        // Mettre à jour l'affichage de la valeur
        document.getElementById(`value-df${dfNumber}-${index}`).textContent = numValue;

        // Recalculer les scores
        calculateDFScores(dfNumber);

        // Mettre à jour les graphiques
        updateDFCharts(dfNumber);

        // Mettre à jour les statistiques
        updateDFStats(dfNumber);

        // Mettre à jour la progression
        updateProgress();
    }

    // Calculer les scores pour un DF
    function calculateDFScores(dfNumber) {
        console.log(`Calculating scores for DF${dfNumber}`);

        // Vérifier que les données existent
        if (!evaluationData[`DF${dfNumber}`]) {
            console.error(`No data found for DF${dfNumber}`);
            return;
        }

        const dfData = evaluationData[`DF${dfNumber}`];
        const inputs = dfData.inputs;

        if (!inputs || !Array.isArray(inputs)) {
            console.error(`Invalid inputs for DF${dfNumber}:`, inputs);
            return;
        }

        const matrix = cobitMatrices[`DF${dfNumber}`];
        if (!matrix) {
            console.error(`No matrix found for DF${dfNumber}`);
            return;
        }

        const scores = [];
        const baselines = [];

        // Calculer les scores
        for (let i = 0; i < cobitData.objectives.length; i++) {
            let score = 0;
            for (let j = 0; j < inputs.length; j++) {
                score += (matrix[i] && matrix[i][j] ? matrix[i][j] : 0) * (inputs[j] || 0);
            }
            scores.push(Math.round(score * 100) / 100);
            baselines.push(2.5); // Baseline par défaut
        }

        evaluationData[`DF${dfNumber}`].scores = scores;
        evaluationData[`DF${dfNumber}`].baselines = baselines;

        console.log(`Scores calculated for DF${dfNumber}:`, scores.slice(0, 5));
    }

    // Mettre à jour les graphiques d'un DF
    function updateDFCharts(dfNumber) {
        calculateDFScores(dfNumber);
        createDFRadarChart(dfNumber);
        createDFBarChart(dfNumber);
    }

    // Créer le graphique radar pour un DF
    function createDFRadarChart(dfNumber) {
        const ctx = document.getElementById(`radarChart-df${dfNumber}`);
        if (!ctx) return;

        const chartKey = `radar-df${dfNumber}`;
        if (charts[chartKey]) {
            charts[chartKey].destroy();
        }

        const domainData = calculateDomainAverages(dfNumber);

        charts[chartKey] = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: domainData.labels,
                datasets: [
                    {
                        label: `Scores DF${dfNumber}`,
                        data: domainData.avgData,
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 2
                    },
                    {
                        label: 'Baseline',
                        data: domainData.baselineData,
                        backgroundColor: 'rgba(107, 114, 128, 0.2)',
                        borderColor: 'rgba(107, 114, 128, 1)',
                        pointBackgroundColor: 'rgba(107, 114, 128, 1)',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 5,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    // Créer le graphique en barres pour un DF
    function createDFBarChart(dfNumber) {
        const ctx = document.getElementById(`barChart-df${dfNumber}`);
        if (!ctx) return;

        const chartKey = `bar-df${dfNumber}`;
        if (charts[chartKey]) {
            charts[chartKey].destroy();
        }

        const scores = evaluationData[`DF${dfNumber}`].scores;
        const topScores = scores.slice(0, 10);
        const topObjectives = cobitData.objectives.slice(0, 10);

        charts[chartKey] = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: topObjectives,
                datasets: [{
                    label: `Impact DF${dfNumber}`,
                    data: topScores,
                    backgroundColor: 'rgba(59, 130, 246, 0.7)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 5
                    },
                    x: {
                        ticks: {
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    }

    // Calculer les moyennes par domaine
    function calculateDomainAverages(dfNumber) {
        const scores = evaluationData[`DF${dfNumber}`].scores;
        const baselines = evaluationData[`DF${dfNumber}`].baselines;

        const domainAverages = {};
        const domainBaselines = {};
        const domainCounts = {};

        // Initialiser
        Object.keys(cobitData.domains).forEach(domain => {
            domainAverages[domain] = 0;
            domainBaselines[domain] = 0;
            domainCounts[domain] = 0;
        });

        // Calculer les moyennes
        cobitData.objectives.forEach((objective, index) => {
            const domain = getDomain(objective);
            if (domain) {
                domainAverages[domain] += scores[index] || 0;
                domainBaselines[domain] += baselines[index] || 0;
                domainCounts[domain]++;
            }
        });

        // Finaliser les moyennes
        const labels = [];
        const avgData = [];
        const baselineData = [];

        Object.keys(cobitData.domains).forEach(domain => {
            if (domainCounts[domain] > 0) {
                labels.push(domain);
                avgData.push(Math.round((domainAverages[domain] / domainCounts[domain]) * 100) / 100);
                baselineData.push(Math.round((domainBaselines[domain] / domainCounts[domain]) * 100) / 100);
            }
        });

        return { labels, avgData, baselineData };
    }

    // Obtenir le domaine d'un objectif
    function getDomain(objective) {
        for (const [domain, objectives] of Object.entries(cobitData.domains)) {
            if (objectives.includes(objective)) {
                return domain;
            }
        }
        return null;
    }

    // Mettre à jour les statistiques d'un DF
    function updateDFStats(dfNumber) {
        const dfData = evaluationData[`DF${dfNumber}`];
        if (!dfData) {
            console.warn(`No data for DF${dfNumber} stats`);
            return;
        }

        const scores = dfData.scores || [];
        const inputs = dfData.inputs || [];

        // Vérifier que les éléments existent
        const avgScoreEl = document.getElementById(`avg-score-df${dfNumber}`);
        const maxImpactEl = document.getElementById(`max-impact-df${dfNumber}`);
        const affectedObjectivesEl = document.getElementById(`affected-objectives-df${dfNumber}`);
        const completionEl = document.getElementById(`completion-df${dfNumber}`);

        if (scores.length > 0) {
            // Score moyen
            const avgScore = scores.reduce((a, b) => a + b, 0) / scores.length;
            if (avgScoreEl) avgScoreEl.textContent = avgScore.toFixed(1);

            // Impact maximum
            const maxImpact = Math.max(...scores);
            if (maxImpactEl) maxImpactEl.textContent = maxImpact.toFixed(1);

            // Objectifs affectés (score > 0.1)
            const affectedObjectives = scores.filter(score => score > 0.1).length;
            if (affectedObjectivesEl) affectedObjectivesEl.textContent = affectedObjectives;
        }

        if (inputs.length > 0) {
            // Complétude (pourcentage d'inputs non-zéro)
            const nonZeroInputs = inputs.filter(input => input > 0).length;
            const completion = Math.round((nonZeroInputs / inputs.length) * 100);
            if (completionEl) completionEl.textContent = completion + '%';
        }
    }

    // Mettre à jour la progression globale
    function updateProgress() {
        let completedDFs = 0;

        designFactors.forEach(df => {
            const dfNumber = df.code.replace('DF', '');
            const inputs = evaluationData[`DF${dfNumber}`].inputs;
            const hasNonZeroInputs = inputs.some(input => input > 0);

            if (hasNonZeroInputs) {
                completedDFs++;
            }
        });

        const percentage = Math.round((completedDFs / designFactors.length) * 100);
        document.getElementById('progress-bar').style.width = percentage + '%';
        document.getElementById('progress-text').textContent = `${completedDFs}/${designFactors.length} complétés`;
    }

    // Mettre à jour tous les graphiques
    function updateAllCharts() {
        designFactors.forEach(df => {
            const dfNumber = df.code.replace('DF', '');
            updateDFCharts(dfNumber);
            updateDFStats(dfNumber);
        });
    }

    // Sauvegarder les données d'un DF
    function saveDFData(dfNumber) {
        const inputs = evaluationData[`DF${dfNumber}`].inputs;

        fetch('/cobit/save-evaluation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                step: dfNumber,
                inputs: inputs
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CobitUtils.showNotification(`DF${dfNumber} sauvegardé avec succès`, 'success');
            } else {
                CobitUtils.showNotification('Erreur lors de la sauvegarde', 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            CobitUtils.showNotification('Erreur lors de la sauvegarde', 'error');
        });
    }

    // Réinitialiser un DF
    function resetDF(dfNumber) {
        if (confirm(`Êtes-vous sûr de vouloir réinitialiser DF${dfNumber} ?`)) {
            const df = designFactors.find(d => d.code === `DF${dfNumber}`);
            if (df) {
                evaluationData[`DF${dfNumber}`].inputs = [...df.defaults];

                // Mettre à jour les contrôles
                df.labels.forEach((label, index) => {
                    const input = document.getElementById(`input-df${dfNumber}-${index}`);
                    const valueDisplay = document.getElementById(`value-df${dfNumber}-${index}`);

                    if (input && valueDisplay) {
                        input.value = df.defaults[index];
                        valueDisplay.textContent = df.defaults[index];
                    }
                });

                updateDFCharts(dfNumber);
                updateDFStats(dfNumber);
                updateProgress();

                CobitUtils.showNotification(`DF${dfNumber} réinitialisé`, 'info');
            }
        }
    }

    // Calculer les résultats globaux
    function calculateGlobalResults() {
        console.log('Calculating global results...');

        const globalScores = new Array(cobitData.objectives.length).fill(0);
        const globalBaselines = new Array(cobitData.objectives.length).fill(0);
        let activeDFs = 0;

        // Vérifier que designFactors est un array
        if (!Array.isArray(designFactors)) {
            console.error('designFactors is not an array:', designFactors);
            return;
        }

        // Agréger tous les DF
        designFactors.forEach(df => {
            if (!df || !df.code) {
                console.error('Invalid DF in global calculation:', df);
                return;
            }

            const dfNumber = df.code.replace('DF', '');
            const dfData = evaluationData[`DF${dfNumber}`];

            if (!dfData) {
                console.warn(`No data for DF${dfNumber}`);
                return;
            }

            const scores = dfData.scores || [];
            const baselines = dfData.baselines || [];

            if (scores.length > 0) {
                activeDFs++;
                scores.forEach((score, index) => {
                    globalScores[index] += score || 0;
                    globalBaselines[index] += baselines[index] || 2.5;
                });
            }
        });

        // Calculer les moyennes
        if (activeDFs > 0) {
            globalScores.forEach((score, index) => {
                globalScores[index] = Math.round((score / activeDFs) * 100) / 100;
                globalBaselines[index] = Math.round((globalBaselines[index] / activeDFs) * 100) / 100;
            });
        }

        // Calculer les priorités
        const results = [];
        let highPriority = 0, mediumPriority = 0, lowPriority = 0;

        cobitData.objectives.forEach((objective, index) => {
            const score = globalScores[index];
            const baseline = globalBaselines[index];
            const gap = score - baseline;
            const absGap = Math.abs(gap);

            let priority = 'L';
            if (absGap > baseline * 0.5) {
                priority = 'H';
                highPriority++;
            } else if (absGap > baseline * 0.2) {
                priority = 'M';
                mediumPriority++;
            } else {
                lowPriority++;
            }

            results.push({
                objective,
                domain: getDomain(objective),
                score: score,
                baseline: baseline,
                gap: Math.round(gap * 100) / 100,
                priority
            });
        });

        // Mettre à jour l'affichage
        document.getElementById('total-objectives').textContent = cobitData.objectives.length;
        document.getElementById('high-priority').textContent = highPriority;
        document.getElementById('medium-priority').textContent = mediumPriority;
        document.getElementById('low-priority').textContent = lowPriority;

        // Créer les graphiques globaux
        createGlobalRadarChart(globalScores, globalBaselines);
        createGapAnalysisChart(results);

        // Remplir le tableau
        populateResultsTable(results);
    }

    // Créer le graphique radar global
    function createGlobalRadarChart(scores, baselines) {
        const ctx = document.getElementById('globalRadarChart');
        if (!ctx) return;

        if (charts.globalRadar) {
            charts.globalRadar.destroy();
        }

        const domainData = calculateGlobalDomainAverages(scores, baselines);

        charts.globalRadar = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: domainData.labels,
                datasets: [
                    {
                        label: 'Scores Globaux',
                        data: domainData.avgData,
                        backgroundColor: 'rgba(34, 197, 94, 0.2)',
                        borderColor: 'rgba(34, 197, 94, 1)',
                        pointBackgroundColor: 'rgba(34, 197, 94, 1)',
                        borderWidth: 3
                    },
                    {
                        label: 'Baseline',
                        data: domainData.baselineData,
                        backgroundColor: 'rgba(107, 114, 128, 0.2)',
                        borderColor: 'rgba(107, 114, 128, 1)',
                        pointBackgroundColor: 'rgba(107, 114, 128, 1)',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 5,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    // Calculer les moyennes globales par domaine
    function calculateGlobalDomainAverages(scores, baselines) {
        const domainAverages = {};
        const domainBaselines = {};
        const domainCounts = {};

        // Initialiser
        Object.keys(cobitData.domains).forEach(domain => {
            domainAverages[domain] = 0;
            domainBaselines[domain] = 0;
            domainCounts[domain] = 0;
        });

        // Calculer
        cobitData.objectives.forEach((objective, index) => {
            const domain = getDomain(objective);
            if (domain) {
                domainAverages[domain] += scores[index] || 0;
                domainBaselines[domain] += baselines[index] || 0;
                domainCounts[domain]++;
            }
        });

        // Finaliser
        const labels = [];
        const avgData = [];
        const baselineData = [];

        Object.keys(cobitData.domains).forEach(domain => {
            if (domainCounts[domain] > 0) {
                labels.push(domain);
                avgData.push(Math.round((domainAverages[domain] / domainCounts[domain]) * 100) / 100);
                baselineData.push(Math.round((domainBaselines[domain] / domainCounts[domain]) * 100) / 100);
            }
        });

        return { labels, avgData, baselineData };
    }

    // Créer le graphique d'analyse des écarts
    function createGapAnalysisChart(results) {
        const ctx = document.getElementById('gapAnalysisChart');
        if (!ctx) return;

        if (charts.gapAnalysis) {
            charts.gapAnalysis.destroy();
        }

        // Trier par écart absolu et prendre les 15 premiers
        const sortedResults = results.sort((a, b) => Math.abs(b.gap) - Math.abs(a.gap)).slice(0, 15);

        charts.gapAnalysis = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: sortedResults.map(r => r.objective),
                datasets: [{
                    label: 'Écart (Score - Baseline)',
                    data: sortedResults.map(r => r.gap),
                    backgroundColor: sortedResults.map(r =>
                        r.gap >= 0 ? 'rgba(34, 197, 94, 0.7)' : 'rgba(239, 68, 68, 0.7)'
                    ),
                    borderColor: sortedResults.map(r =>
                        r.gap >= 0 ? 'rgba(34, 197, 94, 1)' : 'rgba(239, 68, 68, 1)'
                    ),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    },
                    x: {
                        ticks: {
                            maxRotation: 45
                        }
                    }
                }
            }
        });
    }

    // Remplir le tableau des résultats
    function populateResultsTable(results) {
        const tbody = document.getElementById('resultsTableBody');
        if (!tbody) {
            console.warn('Table body not found');
            return;
        }

        tbody.innerHTML = '';

        results.forEach(result => {
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';

            const priorityClass = result.priority === 'H' ? 'bg-red-100 text-red-800' :
                                 result.priority === 'M' ? 'bg-yellow-100 text-yellow-800' :
                                 'bg-green-100 text-green-800';

            const priorityText = CobitUtils.getPriorityText(result.priority);

            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${result.objective}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${result.domain}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${CobitUtils.formatNumber(result.score)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${CobitUtils.formatNumber(result.baseline)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm ${result.gap >= 0 ? 'text-green-600' : 'text-red-600'}">
                    ${result.gap > 0 ? '+' : ''}${CobitUtils.formatNumber(result.gap)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${priorityClass}">
                        ${priorityText}
                    </span>
                </td>
            `;

            tbody.appendChild(row);
        });

        console.log(`Table populated with ${results.length} results`);
    }

    // Fonctions utilitaires
    function saveAllData() {
        let savedCount = 0;
        const totalDFs = designFactors.length;

        designFactors.forEach((df, index) => {
            const dfNumber = df.code.replace('DF', '');
            setTimeout(() => {
                saveDFData(dfNumber);
                savedCount++;

                if (savedCount === totalDFs) {
                    CobitUtils.showNotification('Toutes les données sauvegardées', 'success');
                }
            }, index * 200);
        });
    }

    function exportData() {
        const exportData = {
            timestamp: new Date().toISOString(),
            version: '1.0',
            designFactors: designFactors,
            evaluationData: evaluationData
        };

        CobitExport.exportToJSON(exportData, `cobit-evaluation-${new Date().toISOString().split('T')[0]}.json`);
    }

    function resetAllData() {
        if (confirm('Êtes-vous sûr de vouloir réinitialiser toutes les données ?')) {
            designFactors.forEach(df => {
                const dfNumber = df.code.replace('DF', '');
                resetDF(dfNumber);
            });

            CobitUtils.showNotification('Toutes les données réinitialisées', 'info');
        }
    }

    function exportResults() {
        calculateGlobalResults();

        // Préparer les données pour l'export
        const exportData = {
            timestamp: new Date().toISOString(),
            summary: {
                totalObjectives: document.getElementById('total-objectives')?.textContent || '0',
                highPriority: document.getElementById('high-priority')?.textContent || '0',
                mediumPriority: document.getElementById('medium-priority')?.textContent || '0',
                lowPriority: document.getElementById('low-priority')?.textContent || '0'
            },
            designFactors: designFactors.map(df => ({
                code: df.code,
                title: df.title,
                inputs: evaluationData[df.code] ? evaluationData[df.code].inputs : []
            })),
            evaluationData: evaluationData
        };

        CobitExport.exportToJSON(exportData, `cobit-results-${new Date().toISOString().split('T')[0]}.json`);
    }

    function exportJSON() {
        calculateGlobalResults();
        exportData();
    }

    function sortResults(criteria) {
        const tbody = document.getElementById('resultsTableBody');
        if (!tbody) {
            console.warn('Table body not found for sorting');
            return;
        }

        const rows = Array.from(tbody.querySelectorAll('tr'));
        if (rows.length === 0) {
            CobitUtils.showNotification('Aucune donnée à trier', 'warning');
            return;
        }

        rows.sort((a, b) => {
            let aValue, bValue;

            switch (criteria) {
                case 'objective':
                    aValue = a.cells[0].textContent.trim();
                    bValue = b.cells[0].textContent.trim();
                    return aValue.localeCompare(bValue);

                case 'priority':
                    aValue = a.cells[5].textContent.trim();
                    bValue = b.cells[5].textContent.trim();
                    const priorityOrder = { 'Haute': 3, 'Moyenne': 2, 'Faible': 1 };
                    return (priorityOrder[bValue] || 0) - (priorityOrder[aValue] || 0);

                case 'gap':
                    aValue = parseFloat(a.cells[4].textContent.replace('+', ''));
                    bValue = parseFloat(b.cells[4].textContent.replace('+', ''));
                    return Math.abs(bValue) - Math.abs(aValue);

                case 'score':
                    aValue = parseFloat(a.cells[2].textContent);
                    bValue = parseFloat(b.cells[2].textContent);
                    return bValue - aValue;

                default:
                    return 0;
            }
        });

        tbody.innerHTML = '';
        rows.forEach(row => tbody.appendChild(row));

        CobitUtils.showNotification(`Tableau trié par ${criteria}`, 'info');
    }
</script>
@endpush
