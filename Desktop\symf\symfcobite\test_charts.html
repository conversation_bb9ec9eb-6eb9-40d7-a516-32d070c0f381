<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Charts</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .chart-container {
            width: 400px;
            height: 400px;
            margin: 20px;
            border: 1px solid #ccc;
            padding: 10px;
        }
    </style>
</head>
<body>
    <h1>Test des Graphiques</h1>
    
    <div class="chart-container">
        <h3>Graphique Radar Test</h3>
        <canvas id="df1_radarChart"></canvas>
    </div>
    
    <div class="chart-container">
        <h3>Graphique Barres Test</h3>
        <canvas id="df1_barChart"></canvas>
    </div>
    
    <button onclick="createTestCharts()">Créer les graphiques</button>
    <button onclick="updateTestCharts()">Mettre à jour</button>
    
    <script>
        let testCharts = {};
        
        function createTestCharts() {
            console.log('Creating test charts...');
            console.log('Chart.js available:', typeof Chart !== 'undefined');
            
            // Test Radar Chart
            const radarCtx = document.getElementById('df1_radarChart');
            console.log('Radar context:', radarCtx);
            
            if (radarCtx) {
                try {
                    testCharts.radar = new Chart(radarCtx, {
                        type: 'radar',
                        data: {
                            labels: ['EDM', 'APO', 'BAI', 'DSS', 'MEA'],
                            datasets: [{
                                label: 'Test Data',
                                data: [3, 4, 2, 5, 3],
                                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Test Radar Chart'
                                }
                            }
                        }
                    });
                    console.log('Radar chart created successfully');
                } catch (error) {
                    console.error('Error creating radar chart:', error);
                }
            }
            
            // Test Bar Chart
            const barCtx = document.getElementById('df1_barChart');
            console.log('Bar context:', barCtx);
            
            if (barCtx) {
                try {
                    testCharts.bar = new Chart(barCtx, {
                        type: 'bar',
                        data: {
                            labels: ['Obj1', 'Obj2', 'Obj3', 'Obj4', 'Obj5'],
                            datasets: [{
                                label: 'Test Scores',
                                data: [12, 19, 3, 5, 2],
                                backgroundColor: 'rgba(59, 130, 246, 0.7)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Test Bar Chart'
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                    console.log('Bar chart created successfully');
                } catch (error) {
                    console.error('Error creating bar chart:', error);
                }
            }
        }
        
        function updateTestCharts() {
            console.log('Updating test charts...');
            
            if (testCharts.radar) {
                testCharts.radar.data.datasets[0].data = [
                    Math.random() * 5,
                    Math.random() * 5,
                    Math.random() * 5,
                    Math.random() * 5,
                    Math.random() * 5
                ];
                testCharts.radar.update();
            }
            
            if (testCharts.bar) {
                testCharts.bar.data.datasets[0].data = [
                    Math.random() * 20,
                    Math.random() * 20,
                    Math.random() * 20,
                    Math.random() * 20,
                    Math.random() * 20
                ];
                testCharts.bar.update();
            }
        }
        
        // Auto-create charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, creating charts...');
            setTimeout(createTestCharts, 100);
        });
    </script>
</body>
</html>
