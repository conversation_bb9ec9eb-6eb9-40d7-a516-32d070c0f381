<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>COBIT 2019 Design Toolkit</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary: #1E40AF;
            --secondary: #3B82F6;
            --accent: #10B981;
            --dark: #1F2937;
            --light: #F9FAFB;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f0f4f8 0%, #e6f0fa 100%);
            color: #1F2937;
        }
        
        .slider-thumb::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: var(--primary);
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .slider-thumb::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: var(--primary);
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .progress-step {
            transition: all 0.3s ease;
            position: relative;
        }
        
        .progress-step.active {
            background-color: var(--primary);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 6px rgba(30, 64, 175, 0.3);
        }
        
        .progress-step.completed {
            background-color: var(--accent);
            color: white;
        }
        
        .progress-step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 30px;
            height: 2px;
            background-color: #D1D5DB;
            z-index: -1;
        }
        
        .progress-step:last-child::after {
            display: none;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .table-hover tbody tr:hover {
            background-color: #f8fafc;
        }
        
        .tab-btn {
            padding: 10px 20px;
            border-radius: 6px 6px 0 0;
            background-color: #e5e7eb;
            border: 1px solid #d1d5db;
            border-bottom: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .tab-btn.active {
            background-color: #fff;
            border-color: #d1d5db;
            color: var(--primary);
            font-weight: 600;
            position: relative;
            top: 1px;
        }
        
        .help-panel {
            background-color: #f0f9ff;
            border-left: 4px solid var(--primary);
            border-radius: 0 6px 6px 0;
        }
        
        .priority-high {
            background-color: #fee2e2;
            color: #b91c1c;
        }
        
        .priority-medium {
            background-color: #fef3c7;
            color: #b45309;
        }
        
        .priority-low {
            background-color: #dcfce7;
            color: #15803d;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border: 1px solid #E5E7EB;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 16px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            padding: 15px;
            transition: all 0.3s ease;
            border-radius: 8px;
        }

        .chart-container.updating {
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
            transform: scale(1.02);
        }

        .real-time-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(45deg, #10B981, #34D399);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-30px,0); }
            70% { transform: translate3d(0,-15px,0); }
            90% { transform: translate3d(0,-4px,0); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translate3d(0, 40px, 0);
            }
            to {
                opacity: 1;
                transform: translate3d(0, 0, 0);
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.6s ease-out;
        }

        .stat-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .stat-card:hover::before {
            left: 100%;
        }

        .input-glow {
            transition: all 0.3s ease;
        }

        .input-glow:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3B82F6;
        }
        
        .domain-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 5px;
        }
        
        .domain-edm { background: #8B5CF6; color: white; }
        .domain-apo { background: #3B82F6; color: white; }
        .domain-bai { background: #10B981; color: white; }
        .domain-dss { background: #F59E0B; color: white; }
        .domain-mea { background: #EF4444; color: white; }
        
        .btn-primary {
            background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(59, 130, 246, 0.4);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .objective-card {
            border-left: 4px solid var(--primary);
            transition: all 0.3s ease;
        }
        
        .objective-card:hover {
            border-left-width: 8px;
            background-color: #f0f7ff;
        }
        
        .heatmap-cell {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: inline-block;
            margin: 2px;
            text-align: center;
            line-height: 24px;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        
        @media (max-width: 768px) {
            .progress-step::after {
                width: 15px;
            }
            
            .progress-step {
                width: 30px;
                height: 30px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Header -->
    <header class="glass-effect shadow-sm border-b sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-800 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-xl shadow-lg">
                        <i class="fas fa-chart-network"></i>
                    </div>
                    <div class="ml-4">
                        <h1 class="text-xl font-bold text-gray-900">COBIT 2019 Design Toolkit</h1>
                        <p class="text-sm text-gray-600">Évaluation de la Gouvernance IT</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button id="helpBtn" class="btn-primary">
                        <i class="fas fa-question-circle mr-2"></i>Aide
                    </button>
                    <button id="importBtn" class="btn-primary">
                        <i class="fas fa-file-import mr-2"></i>Importer
                    </button>
                    <button id="saveBtn" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>Sauvegarder
                    </button>
                    <button id="resetBtn" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all font-medium">
                        <i class="fas fa-sync-alt mr-2"></i>Reset
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Progress Bar -->
    <div class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex space-x-1 overflow-x-auto py-2">
                    <div class="progress-step active w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="1">1</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="2">2</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="3">3</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="4">4</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="5">5</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="6">6</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="7">7</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="8">8</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="9">9</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="10">10</div>
                    <div class="progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-600" data-step="11"><i class="fas fa-chart-bar"></i></div>
                </div>
                <div class="ml-4 text-sm text-gray-600 flex items-center">
                    <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                        Étape <span id="currentStepText" class="font-bold">1</span> sur 11
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div id="content" class="fade-in">
            <!-- Content will be dynamically loaded here -->
        </div>
    </main>

    <!-- Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex justify-between items-center">
                <button id="prevBtn" class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                    <i class="fas fa-arrow-left mr-2"></i>Précédent
                </button>
                <div class="text-sm text-gray-600">
                    <span id="validationMessage" class="text-blue-600 font-medium"></span>
                </div>
                <button id="nextBtn" class="btn-primary">
                    Suivant<i class="fas fa-arrow-right ml-2"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Application State
        const appState = {
            currentStep: 1,
            data: {},
            results: {},
            finalResults: {},
            dfWeights: [1.2, 1.1, 1.0, 0.9, 0.8, 1.0, 1.1, 0.9, 0.8, 1.0] // Poids pour chaque DF
        };
        
        // Initialize data from configurations
        function initializeAppState() {
            for (let i = 1; i <= 10; i++) {
                const dfKey = `DF${i}`;
                const config = cobitData.inputConfigs[dfKey];
                appState.data[dfKey] = {
                    inputs: [...config.defaults],
                    labels: [...config.labels],
                    type: config.type
                };
            }
        }

        // COBIT Data (extracted from Excel)
        const cobitData = {
            objectives: [
                "EDM01", "EDM02", "EDM03", "EDM04", "EDM05",
                "APO01", "APO02", "APO03", "APO04", "APO05", "APO06", "APO07", "APO08", "APO09", "APO10", "APO11", "APO12", "APO13", "APO14",
                "BAI01", "BAI02", "BAI03", "BAI04", "BAI05", "BAI06", "BAI07", "BAI08", "BAI09", "BAI10", "BAI11",
                "DSS01", "DSS02", "DSS03", "DSS04", "DSS05", "DSS06",
                "MEA01", "MEA02", "MEA03", "MEA04"
            ],
            
            // DFxmap matrices for calculations
            matrices: {
                DF1map: [
                    [1.2, 1.0, 1.5, 1.5], [1.5, 1.0, 2.0, 3.5], [1.0, 1.0, 1.0, 2.0], [1.5, 1.0, 4.0, 1.0], [1.5, 1.5, 1.0, 2.0],
                    [1.0, 1.0, 1.0, 1.0], [3.5, 3.5, 1.5, 1.0], [4.0, 2.0, 1.0, 1.0], [1.0, 4.0, 1.0, 1.0], [3.5, 4.0, 2.5, 1.0],
                    [1.5, 1.0, 4.0, 1.0], [2.0, 1.0, 1.0, 1.0], [1.0, 1.5, 1.0, 3.5], [1.0, 1.0, 1.5, 4.0], [1.0, 1.0, 3.5, 1.5],
                    [1.0, 1.0, 1.0, 4.0], [1.0, 1.5, 1.0, 2.5], [1.0, 1.0, 1.0, 2.5], [1.0, 1.0, 1.0, 1.0], [4.0, 2.0, 1.5, 1.5],
                    [1.0, 1.0, 1.5, 1.0], [1.0, 1.0, 1.5, 1.0], [1.0, 1.0, 1.0, 3.0], [4.0, 2.0, 1.0, 1.5], [2.0, 2.0, 1.0, 1.5],
                    [1.5, 2.0, 1.0, 1.5], [1.0, 3.5, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0], [3.5, 3.0, 1.5, 1.0],
                    [1.0, 1.0, 1.0, 1.5], [1.0, 1.0, 1.0, 4.0], [1.0, 1.0, 1.0, 3.0], [1.0, 1.0, 1.0, 4.0], [1.0, 1.0, 1.0, 2.5],
                    [1.0, 1.0, 1.0, 1.5], [1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0]
                ],
                DF2map: [
                    [null, "Agile portfolio of competitive products and services", "Managed business risks", "Compliance with external laws and regulations"],
                    [null, 4, 2, 2],
                    [null, null, null, null],
                    [null, null, null, null],
                    [null, null, null, null],
                    [null, "AG01", "AG02", "AG03"],
                    [null, "IT compliance and support for business compliance with external laws and regulations", "Managed Technology & Information related risks", "Realized benefits from IT-enabled investments and services portfolio"],
                    ["Portfolio of agile and competitive products and services", 0, 0, 1],
                    ["Managed business risks", 1, 2, 0],
                    ["Compliance with external laws and regulations", 2, 0, 0],
                    ["Transparency and accuracy of financial information", 0, 0, 0],
                    ["Customer-oriented service culture", 0, 0, 1],
                    ["Business service continuity and availability", 0, 1, 0],
                    ["Accuracy (Quality?) of Management Information", 0, 0, 0],
                    ["Optimization of business process functionality", 0, 0, 1],
                    ["Optimization of business process costs", 0, 0, 1],
                    ["Staff skills, motivation and productivity", 0, 0, 0],
                    ["Compliance with internal policies", 1, 0, 0],
                    ["Managed business transformation programs", 0, 0, 2],
                    ["Product and business innovation", 0, 0, 0],
                    [null, null, null, null],
                    [null, null, null, null],
                    [null, "AG01", "AG02", "AG03"],
                    [null, "IT compliance and support for business compliance with external laws and regulations", "Managed Technology & Information related risks", "Realized benefits from IT-enabled investments and services portfolio"],
                    [null, 8, 7, 20],
                    [null, null, null, null],
                    [null, null, null, null],
                    [null, "EDM01", "EDM02", "EDM03"],
                    [null, "Ensured Governance Framework Setting & Maintenance", "Ensured Benefits Delivery", "Ensured Risk Optimization"],
                    ["IT compliance and support for business compliance with external laws and regulations", 1, 0, 1],
                    ["Managed Technology & Information related risks", 1, 0, 2],
                    ["Realized benefits from IT-enabled investments and services portfolio", 2, 2, 0],
                    ["Quality of technology related financial information", 0, 0, 0],
                    ["Delivery of IT services in line with business requirements", 0, 1, 0],
                    ["Agility to turn business requirements into operational solutions", 0, 1, 0],
                    ["Security of information, processing infrastructure and applications", 0, 0, 2],
                    ["Enablement and support of business processes by Integrating applications and technology", 1, 1, 0],
                    ["Delivery of programs on time, on budget, and meeting requirements and quality standards", 0, 0, 0],
                    ["Quality of IT Management Information", 0, 0, 0],
                    ["IT compliance with internal policies", 1, 0, 1]
                ],
                DF3map: [
                    [3, 2, 3, 0], [3, 2, 0, 0], [2, 2, 0, 0], [3, 0, 4, 3], [3, 1, 3, 0],
                    [2, 3, 2, 0], [2, 0, 0, 0], [2, 0, 0, 0], [0, 0, 0, 0], [4, 2, 2, 0],
                    [2, 3, 4, 0], [0, 0, 0, 4], [0, 0, 0, 2], [0, 0, 2, 0], [0, 2, 3, 0],
                    [0, 3, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 4, 0, 0],
                    [2, 2, 0, 0], [0, 3, 0, 0], [0, 1, 0, 0], [0, 2, 0, 2], [0, 0, 0, 0],
                    [0, 0, 0, 0], [0, 0, 0, 2], [0, 0, 0, 0], [0, 0, 0, 0], [0, 4, 0, 0],
                    [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0],
                    [0, 0, 0, 0], [1, 2, 2, 0], [1, 2, 2, 0], [0, 1, 0, 0]
                ],
                DF4map: [
                    [3, 3, 1, 1], [2.5, 3, 1, 1], [1, 1, 2, 1], [1, 1, 1, 1], [1, 1, 1, 1],
                    [2, 1, 2, 1], [1.5, 1.5, 1.5, 1.5], [1, 1.5, 1, 2], [1, 1, 1, 1], [3, 3, 1, 1.5],
                    [3.5, 2, 1, 1.5], [1.5, 1, 1, 1], [2.5, 2, 1, 2.5], [2, 1.5, 2, 4], [1, 1, 2, 4],
                    [1, 1, 3, 1.5], [1, 0.5, 2.5, 1.5], [0, 0, 3.5, 1], [1, 1.5, 3, 1], [0, 1, 1.5, 0],
                    [0, 3, 0, 0], [1, 2, 2, 0], [0.5, 0, 2, 3], [1, 3, 0, 0], [0, 0, 2.5, 3],
                    [0, 1, 2, 2], [0, 0, 0, 1.5], [0.5, 0.5, 1, 0], [0, 0, 2.5, 2], [1, 2, 2.5, 0],
                    [0, 0, 2.5, 2], [1, 1, 4, 3], [0, 1, 3, 3], [0, 0, 3, 1], [0, 0, 4, 2],
                    [0, 1, 0.5, 0], [1, 1.5, 2, 2], [0, 0, 2, 2], [0, 0, 2, 2], [1, 1, 3, 1.5]
                ],
                DF5map: [
                    [3, 1], [1, 1], [4, 1], [1, 1], [2, 1],
                    [3, 1], [1, 1], [3, 1], [1, 1], [1, 1],
                    [1, 1], [2, 1], [1, 1], [2, 1], [3, 1],
                    [2, 1], [4, 1], [4, 1], [3, 1], [1, 1],
                    [1, 1], [1, 1], [2, 1], [1, 1], [3, 1],
                    [1, 1], [1, 1], [1, 1], [3, 1], [1, 1],
                    [1, 1], [3, 1], [2, 1], [4, 1], [3, 1],
                    [3, 1], [3, 1], [2, 1], [3, 1], [3, 1]
                ],
                DF6map: [
                    [3, 2, 1, null], [1, 1, 1, null], [4, 2, 1, null], [1, 1, 1, null], [1.5, 1, 1, null],
                    [2, 1.5, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1.5, 1, 1, null],
                    [1, 1, 1, null], [4, 2, 1, null], [1.5, 1, 1, null], [2, 1.5, 1, null], [1, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1.5, 1, 1, null], [2, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [4, 2, 1, null], [3.5, 2, 1, null]
                ],
                DF7map: [
                    [1, 2, 1.5, 4], [1, 1, 2.5, 3], [1, 3, 1, 3], [1, 1, 1, 2], [1, 1, 1, 2],
                    [1, 1.5, 1.5, 2.5], [1, 1, 3, 3], [1, 1, 2, 2], [0.5, 1, 3.5, 4], [1, 1, 2.5, 3],
                    [1, 1, 1, 2], [1, 1, 1, 1.5], [1, 1, 2, 2.5], [1, 2, 1.5, 2], [1, 2.5, 1.5, 2],
                    [1, 1.5, 1.5, 2], [1, 2.5, 1, 3], [1, 2, 1.5, 3], [1, 1.5, 1.5, 2.5], [1, 1, 2, 2.5],
                    [1, 1, 3, 3], [1, 1, 3, 3], [1, 2.5, 1.5, 2], [1, 1, 1, 2], [1, 2.5, 1, 2],
                    [1, 1, 2, 2], [1, 1, 1, 2], [1, 1, 1, 2], [1, 1.5, 1, 2], [1, 1, 2, 2],
                    [1, 3.5, 1, 3], [1, 3, 1.5, 3], [1, 3, 1.5, 3.5], [1, 3, 1.5, 3.5], [1.5, 2.5, 1.5, 3.5],
                    [1, 1, 1, 2.5], [1, 1, 1, 2], [1, 1, 1, 2], [1, 1, 1, 1.5], [1, 1, 1, 2]
                ],
                DF8map: [
                    [1, 1, 1, null], [1, 1, 1, null], [1, 2, 1, null], [1, 1, 1, null], [1, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [4, 4, 1, null], [4, 4, 1, null],
                    [1, 1, 1, null], [2, 2, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null],
                    [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null],
                    [3, 3, 1, null], [1, 1, 1, null], [1, 1, 1, null], [1, 1, 1, null]
                ],
                DF9map: [
                    [1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1],
                    [1, 1, 1], [1, 1, 1], [1, 2, 1], [1, 1, 1], [1, 1, 1],
                    [1, 1, 1], [1, 1.5, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1],
                    [1, 1, 1], [1, 1.5, 1], [1, 1, 1], [1, 1, 1], [2, 1.5, 1],
                    [3.5, 2, 1], [4, 3, 1], [1, 1, 1], [2.5, 1.5, 1], [3.5, 2, 1],
                    [2.5, 2.5, 1], [1, 1, 1], [1, 1, 1], [1.5, 2, 1], [2.5, 1, 1],
                    [1, 2.5, 1], [1, 1.5, 1], [1, 1.5, 1], [1, 1, 1], [1, 1, 1],
                    [1, 1, 1], [1.5, 1.5, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]
                ],
                DF10map: [
                    [3.5, 2.5, 1.5], [4, 2.5, 1.5], [1.5, 1, 1], [2.5, 2, 1.5], [1.5, 1, 1],
                    [2.5, 1.5, 1], [4, 3, 1.5], [2, 1, 1], [4, 3, 1], [4, 2.5, 1],
                    [1, 1.5, 1], [2.5, 1, 1], [3, 1.5, 1], [1.5, 1.5, 1], [2.5, 1.5, 1],
                    [1.5, 1.5, 1], [2, 1.5, 1], [1, 1, 1], [2.5, 2, 1], [4, 3, 1.5],
                    [3.5, 2.5, 1], [4, 2.5, 1], [1.5, 1.5, 1], [3, 2, 1], [2.5, 2, 1],
                    [3.5, 2.5, 1], [1.5, 1, 1], [1, 1, 1], [1.5, 1, 1], [3.5, 2.5, 1],
                    [1, 1, 1], [1, 1, 1], [1.5, 1, 1], [1.5, 1, 1], [1.5, 1, 1],
                    [1, 1, 1], [3, 2, 1], [1, 1, 1], [1, 1, 1], [1, 1, 1]
                ]
            },
            
            // Input configurations for each DF
            inputConfigs: {
                DF1: {
                    type: 'slider',
                    min: 1,
                    max: 5,
                    step: 1,
                    labels: ['Croissance', 'Stabilité', 'Coût', 'Innovation'],
                    defaults: [3, 3, 3, 3]
                },
                DF2: {
                    type: 'dropdown',
                    options: [
                        {value: 1, label: 'Très faible'},
                        {value: 2, label: 'Faible'},
                        {value: 3, label: 'Moyen'},
                        {value: 4, label: 'Élevé'}
                    ],
                    labels: ['Portefeuille agile', 'Risques métier', 'Conformité réglementaire', 'Objectif 4'],
                    defaults: [1, 1, 1, 1]
                },
                DF3: {
                    type: 'slider',
                    min: 1,
                    max: 5,
                    step: 1,
                    labels: ['Investissement IT', 'Gestion programmes', 'Coûts IT', 'Expertise IT'],
                    defaults: [3, 3, 3, 3]
                },
                DF4: {
                    type: 'slider',
                    min: 1,
                    max: 5,
                    step: 1,
                    labels: ['Problème IT 1', 'Problème IT 2', 'Problème IT 3', 'Problème IT 4'],
                    defaults: [3, 3, 3, 3]
                },
                DF5: {
                    type: 'checkbox',
                    labels: ['Menaces externes', 'Menaces internes'],
                    defaults: [0.5, 0.5]
                },
                DF6: {
                    type: 'slider',
                    min: 0,
                    max: 1,
                    step: 0.1,
                    labels: ['Exigences réglementaires', 'Exigences sectorielles', 'Exigences internes'],
                    defaults: [0.5, 0.5, 0.5]
                },
                DF7: {
                    type: 'slider',
                    min: 1,
                    max: 4,
                    step: 1,
                    labels: ['Support', 'Factory', 'Turnaround'],
                    defaults: [3, 3, 3]
                },
                DF8: {
                    type: 'dropdown',
                    options: [
                        {value: 1, label: 'Interne uniquement'},
                        {value: 2, label: 'Mixte'},
                        {value: 3, label: 'Externe principalement'}
                    ],
                    labels: ['Modèle interne', 'Modèle externe'],
                    defaults: [1, 1]
                },
                DF9: {
                    type: 'slider',
                    min: 0,
                    max: 1,
                    step: 0.1,
                    labels: ['Méthodes agiles', 'DevOps', 'Méthodes traditionnelles'],
                    defaults: [0.5, 0.5, 0.5]
                },
                DF10: {
                    type: 'slider',
                    min: 0,
                    max: 1,
                    step: 0.1,
                    labels: ['Petite entreprise', 'Moyenne entreprise', 'Grande entreprise'],
                    defaults: [0.5, 0.5, 0.5]
                }
            },
            
            // Domain classification
            domains: {
                EDM: ['EDM01', 'EDM02', 'EDM03', 'EDM04', 'EDM05'],
                APO: ['APO01', 'APO02', 'APO03', 'APO04', 'APO05', 'APO06', 'APO07', 'APO08', 'APO09', 'APO10', 'APO11', 'APO12', 'APO13', 'APO14'],
                BAI: ['BAI01', 'BAI02', 'BAI03', 'BAI04', 'BAI05', 'BAI06', 'BAI07', 'BAI08', 'BAI09', 'BAI10', 'BAI11'],
                DSS: ['DSS01', 'DSS02', 'DSS03', 'DSS04', 'DSS05', 'DSS06'],
                MEA: ['MEA01', 'MEA02', 'MEA03', 'MEA04']
            }
        };
        
        // Matrix multiplication function
        function matrixMultiply(matrix, vector) {
            const result = [];
            for (let i = 0; i < matrix.length; i++) {
                let sum = 0;
                for (let j = 0; j < vector.length && j < matrix[i].length; j++) {
                    const matrixValue = matrix[i][j];
                    const vectorValue = vector[j];
                    if (matrixValue !== null && matrixValue !== undefined && 
                        vectorValue !== null && vectorValue !== undefined) {
                        sum += matrixValue * vectorValue;
                    }
                }
                result.push(sum);
            }
            return result;
        }
        
        // Calculate baseline scores (using default values)
        function calculateBaseline(dfNumber) {
            const dfKey = `DF${dfNumber}`;
            const matrix = cobitData.matrices[`${dfKey}map`];
            const config = cobitData.inputConfigs[dfKey];
            
            if (!matrix || !config) return [];
            
            const baselineInputs = config.defaults.slice();
            return matrixMultiply(matrix, baselineInputs);
        }
        
        // Calculate scores for current inputs
        function calculateScores(dfNumber, inputs) {
            const dfKey = `DF${dfNumber}`;
            const matrix = cobitData.matrices[`${dfKey}map`];
            
            if (!matrix) return [];
            
            return matrixMultiply(matrix, inputs);
        }
        
        // Calculate Relative Importance (RI) with normalization
        function calculateRI(score, baseline, weight = 1) {
            if (baseline === 0) return 0;
            
            // Normalize the values to avoid extreme percentages
            const normalizedScore = Math.min(score, baseline * 2);
            const normalizedBaseline = Math.max(baseline, 0.1);
            
            // Calculate the gap percentage
            const gap = normalizedScore - normalizedBaseline;
            const gapPercentage = (gap / normalizedBaseline) * 100;
            
            // Apply weight and round to nearest 5
            const ri = gapPercentage * weight;
            return Math.round(ri / 5) * 5;
        }
        
        // Test function to create simple charts
        function createTestChart() {
            console.log('Creating test chart...');
            const testCtx = document.getElementById('df1_radarChart');
            if (testCtx) {
                try {
                    new Chart(testCtx, {
                        type: 'radar',
                        data: {
                            labels: ['EDM', 'APO', 'BAI', 'DSS', 'MEA'],
                            datasets: [{
                                label: 'Test',
                                data: [3, 4, 2, 5, 3],
                                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                title: {
                                    display: true,
                                    text: 'Test Chart'
                                }
                            }
                        }
                    });
                    console.log('Test chart created successfully');
                } catch (error) {
                    console.error('Error creating test chart:', error);
                }
            } else {
                console.log('Test chart element not found');
            }
        }

        // Initialize the application
        function init() {
            console.log('Initializing application...');
            console.log('Chart.js available:', typeof Chart !== 'undefined');

            initializeAppState();
            loadFromLocalStorage();
            renderCurrentStep();
            setupEventListeners();

            // Test chart creation after a delay
            setTimeout(() => {
                console.log('Testing chart creation...');
                createTestChart();
            }, 1000);

            console.log('Application initialized');
        }

        // Event Listeners
        function setupEventListeners() {
            document.getElementById('nextBtn').addEventListener('click', nextStep);
            document.getElementById('prevBtn').addEventListener('click', prevStep);
            document.getElementById('saveBtn').addEventListener('click', saveToLocalStorage);
            document.getElementById('importBtn').addEventListener('click', importFromJSON);
            document.getElementById('resetBtn').addEventListener('click', resetAllData);
            document.getElementById('helpBtn').addEventListener('click', () => {
                if (appState.currentStep <= 10) {
                    showHelp(appState.currentStep);
                } else {
                    alert('Canvas Final: Cette vue présente la synthèse de votre évaluation COBIT 2019 avec les scores finaux, écarts et priorités pour chaque objectif.');
                }
            });
        }

        // Navigation functions
        function nextStep() {
            if (validateCurrentStep()) {
                if (appState.currentStep < 11) {
                    appState.currentStep++;
                    updateProgressBar();
                    renderCurrentStep();
                    saveToLocalStorage();
                }
            }
        }

        function prevStep() {
            if (appState.currentStep > 1) {
                appState.currentStep--;
                updateProgressBar();
                renderCurrentStep();
            }
        }

        function updateProgressBar() {
            // Update progress steps
            document.querySelectorAll('.progress-step').forEach((step, index) => {
                const stepNumber = index + 1;
                step.classList.remove('active', 'completed');
                
                if (stepNumber < appState.currentStep) {
                    step.classList.add('completed');
                } else if (stepNumber === appState.currentStep) {
                    step.classList.add('active');
                }
            });

            // Update step text
            document.getElementById('currentStepText').textContent = appState.currentStep;

            // Update navigation buttons
            document.getElementById('prevBtn').disabled = appState.currentStep === 1;
            document.getElementById('nextBtn').textContent = appState.currentStep === 11 ? 'Terminer' : 'Suivant →';
        }

        function validateCurrentStep() {
            if (appState.currentStep <= 10) {
                const dfKey = `DF${appState.currentStep}`;
                const inputs = appState.data[dfKey].inputs;
                
                // Check if all inputs are filled
                for (let input of inputs) {
                    if (input === null || input === undefined) {
                        document.getElementById('validationMessage').textContent = 'Veuillez remplir tous les champs obligatoires';
                        return false;
                    }
                }
            }
            
            document.getElementById('validationMessage').textContent = '';
            return true;
        }

        function renderCurrentStep() {
            const content = document.getElementById('content');
            content.classList.remove('fade-in');
            
            setTimeout(() => {
                if (appState.currentStep <= 10) {
                    content.innerHTML = renderDesignFactor(appState.currentStep);
                } else {
                    content.innerHTML = renderCanvas();
                }
                content.classList.add('fade-in');
                setupStepEventListeners();
            }, 100);
        }
        
        function getDFTitle(dfNumber) {
            const titles = {
                1: 'Enterprise Strategy',
                2: 'Enterprise Goals', 
                3: 'Risk Profile',
                4: 'IT-Related Issues',
                5: 'Threat Landscape',
                6: 'Compliance Requirements',
                7: 'Role of IT',
                8: 'Sourcing Model for IT',
                9: 'IT Implementation Methods',
                10: 'Enterprise Size'
            };
            return titles[dfNumber] || `Design Factor ${dfNumber}`;
        }
        
        function getDomain(objective) {
            for (const domain in cobitData.domains) {
                if (cobitData.domains[domain].includes(objective)) {
                    return domain;
                }
            }
            return null;
        }

        function renderDesignFactor(dfNumber) {
            const dfKey = `DF${dfNumber}`;
            const dfData = appState.data[dfKey];
            const config = cobitData.inputConfigs[dfKey];
            const dfTitle = getDFTitle(dfNumber);

            let inputsHtml = '';
            
            dfData.inputs.forEach((value, index) => {
                const label = dfData.labels[index];
                const inputId = `${dfKey}_input_${index}`;
                
                if (config.type === 'dropdown') {
                    // Dropdown
                    let optionsHtml = '<option value="">Sélectionner...</option>';
                    config.options.forEach(option => {
                        optionsHtml += `<option value="${option.value}" ${value === option.value ? 'selected' : ''}>${option.label}</option>`;
                    });
                    
                    inputsHtml += `
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">${label}</label>
                            <select id="${inputId}" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 input-glow">
                                ${optionsHtml}
                            </select>
                            <div class="mt-1 text-xs text-gray-500">
                                <i class="fas fa-chart-line mr-1"></i>Affecte les graphiques en temps réel
                            </div>
                        </div>
                    `;
                } else if (config.type === 'checkbox') {
                    // Checkbox
                    inputsHtml += `
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" id="${inputId}" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" ${value > 0.5 ? 'checked' : ''}>
                                <span class="ml-2 text-sm font-medium text-gray-700">${label}</span>
                            </label>
                            <div class="mt-1 text-xs text-gray-500 ml-6">
                                <i class="fas fa-chart-line mr-1"></i>Affecte les graphiques en temps réel
                            </div>
                        </div>
                    `;
                } else {
                    // Slider
                    const min = config.min;
                    const max = config.max;
                    const step = config.step;
                    
                    inputsHtml += `
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">${label}</label>
                            <div class="flex items-center space-x-4">
                                <span class="text-sm text-gray-500">${min}</span>
                                <input type="range" id="${inputId}" min="${min}" max="${max}" step="${step}" value="${value || min}"
                                       class="flex-1 slider-thumb h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer input-glow">
                                <span class="text-sm text-gray-500">${max}</span>
                                <span id="${inputId}_value" class="text-sm font-medium text-blue-600 w-12">${value || min}</span>
                            </div>
                            <div class="mt-1 text-xs text-gray-500">
                                <i class="fas fa-chart-line mr-1"></i>Affecte les graphiques en temps réel
                            </div>
                        </div>
                    `;
                }
            });

            return `
                <div class="bg-white rounded-xl shadow-sm border p-6">
                    <div class="mb-6 flex justify-between items-start">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">DF${dfNumber} - ${dfTitle}</h2>
                            <p class="text-gray-600 mt-2">Configurez les paramètres pour ce Design Factor</p>
                        </div>
                        <button onclick="showHelp(${dfNumber})" class="px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm">
                            <i class="fas fa-question-circle mr-1"></i> Aide
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <div class="card mb-6">
                                <div class="card-header">
                                    <h3 class="text-lg font-semibold">Paramètres d'entrée</h3>
                                </div>
                                <div class="p-6">
                                    ${inputsHtml}
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="text-lg font-semibold">Visualisation par Domaine</h3>
                                </div>
                                <div class="p-4">
                                    <div class="chart-container">
                                        <canvas id="df${dfNumber}_radarChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <div class="card mb-6">
                                <div class="card-header">
                                    <h3 class="text-lg font-semibold">Résultats Calculés</h3>
                                </div>
                                <div class="p-4">
                                    <div class="overflow-x-auto max-h-[300px]">
                                        <table class="min-w-full table-hover">
                                            <thead class="bg-gray-50 sticky top-0">
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Objectif</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Baseline</th>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">RI</th>
                                                </tr>
                                            </thead>
                                            <tbody id="resultsTableBody" class="bg-white divide-y divide-gray-200">
                                                <!-- Results will be populated here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="text-lg font-semibold">Comparaison des Objectifs</h3>
                                </div>
                                <div class="p-4">
                                    <div class="chart-container">
                                        <canvas id="df${dfNumber}_barChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function renderCanvas() {
            return `
                <div class="bg-white rounded-xl shadow-sm border p-6">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">Tableau de Bord COBIT 2019</h2>
                        <p class="text-gray-600 mt-2">Vue synthétique de votre évaluation de gouvernance IT</p>
                    </div>
                    
                    <div class="mb-8">
                        <div class="flex space-x-1 mb-4">
                            <button class="tab-btn active" data-tab="summary">Synthèse</button>
                            <button class="tab-btn" data-tab="objectives">Objectifs</button>
                            <button class="tab-btn" data-tab="visuals">Visualisations</button>
                            <button class="tab-btn" data-tab="heatmap">Relations</button>
                        </div>
                        
                        <div id="tab-content">
                            <!-- Tab content will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="border-t pt-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions</h3>
                        <div class="flex flex-wrap gap-4">
                            <button id="exportPdfBtn" class="btn-primary">
                                <i class="fas fa-file-pdf mr-2"></i>Exporter en PDF
                            </button>
                            <button id="exportJsonBtn" class="btn-primary">
                                <i class="fas fa-file-code mr-2"></i>Télécharger JSON
                            </button>
                            <button id="printBtn" class="btn-primary">
                                <i class="fas fa-print mr-2"></i>Imprimer
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function renderTabContent(tabId) {
            const content = document.getElementById('tab-content');
            if (!content) return;
            
            switch(tabId) {
                case 'summary':
                    content.innerHTML = renderSummaryTab();
                    break;
                case 'objectives':
                    content.innerHTML = renderObjectivesTab();
                    break;
                case 'visuals':
                    content.innerHTML = renderVisualsTab();
                    break;
                case 'heatmap':
                    content.innerHTML = renderHeatmapTab();
                    break;
                default:
                    content.innerHTML = renderSummaryTab();
            }
            
            // Setup event listeners for the tab content
            setupTabEventListeners();
            
            // Create charts if needed
            if (tabId === 'visuals' || tabId === 'heatmap') {
                createChartsWithData();
            }
        }
        
        function renderSummaryTab() {
            const finalResults = appState.finalResults;
            let summaryHtml = '';
            
            if (finalResults && finalResults.sortedByAbsGap) {
                const highPriority = finalResults.sortedByAbsGap.filter(r => r.priority === 'H').length;
                const mediumPriority = finalResults.sortedByAbsGap.filter(r => r.priority === 'M').length;
                const lowPriority = finalResults.sortedByAbsGap.filter(r => r.priority === 'L').length;
                
                // Top 3 objectives by gap
                const topObjectives = finalResults.sortedByAbsGap.slice(0, 3);
                
                summaryHtml = `
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <div class="card">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center text-red-600 mr-4">
                                        <span class="text-xl font-bold">${highPriority}</span>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">Haute Priorité</h3>
                                        <p class="text-gray-600">Objectifs critiques nécessitant une attention immédiate</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center text-yellow-600 mr-4">
                                        <span class="text-xl font-bold">${mediumPriority}</span>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">Moyenne Priorité</h3>
                                        <p class="text-gray-600">Objectifs nécessitant une attention à moyen terme</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-4">
                                        <span class="text-xl font-bold">${lowPriority}</span>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">Basse Priorité</h3>
                                        <p class="text-gray-600">Objectifs bien gérés ou moins critiques</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <div class="card">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Objectifs Clés</h3>
                                <div class="space-y-4">
                                    ${topObjectives.map(obj => `
                                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                            <div>
                                                <span class="font-medium">${obj.objective}</span>
                                                <span class="text-sm text-gray-600 ml-2">Écart: ${obj.gap > 0 ? '+' : ''}${obj.gap.toFixed(1)}</span>
                                            </div>
                                            <span class="px-3 py-1 rounded-full text-xs font-medium ${obj.priority === 'H' ? 'priority-high' : obj.priority === 'M' ? 'priority-medium' : 'priority-low'}">
                                                ${obj.priority === 'H' ? 'Haute' : obj.priority === 'M' ? 'Moyenne' : 'Basse'} priorité
                                            </span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Performance par Domaine</h3>
                                <div class="chart-container">
                                    <canvas id="domainRadarChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recommandations Stratégiques</h3>
                            <ul class="list-disc pl-5 space-y-2">
                                <li>Priorisez les objectifs marqués "Haute priorité" pour une action immédiate</li>
                                <li>Examinez les écarts positifs pour identifier les bonnes pratiques</li>
                                <li>Consultez le détail des objectifs pour des recommandations spécifiques</li>
                                <li>Planifiez une revue trimestrielle des objectifs à moyenne priorité</li>
                                <li>Étudiez les relations entre objectifs dans l'onglet "Relations"</li>
                            </ul>
                        </div>
                    </div>
                `;
            } else {
                summaryHtml = '<div class="card p-6 text-center"><p class="text-gray-600">Aucun résultat disponible. Veuillez compléter toutes les étapes.</p></div>';
            }
            
            return summaryHtml;
        }
        
        function renderObjectivesTab() {
            const finalResults = appState.finalResults;
            let tableHtml = '';
            
            if (finalResults && finalResults.scores) {
                tableHtml = `
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold">Détail des Objectifs</h3>
                        </div>
                        <div class="p-4">
                            <div class="overflow-x-auto">
                                <table class="min-w-full table-hover">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Objectif</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score Final</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Baseline</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Écart</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priorité</th>
                                        </tr>
                                    </thead>
                                    <tbody id="canvasTableBody" class="bg-white divide-y divide-gray-200">
                                        ${cobitData.objectives.map((objective, index) => {
                                            const finalScore = finalResults.scores[index] || 0;
                                            const baseline = finalResults.baselines[index] || 0;
                                            const gap = finalScore - baseline;
                                            const priority = Math.abs(gap) > baseline * 0.5 ? 'H' : 
                                                            Math.abs(gap) > baseline * 0.2 ? 'M' : 'L';
                                            const domain = getDomain(objective);
                                            
                                            const priorityColor = priority === 'H' ? 'priority-high' : 
                                                                 priority === 'M' ? 'priority-medium' : 'priority-low';
                                            
                                            return `
                                                <tr>
                                                    <td class="px-4 py-2 text-sm font-medium text-gray-900">
                                                        ${objective}
                                                        ${domain ? `<span class="domain-badge domain-${domain.toLowerCase()}">${domain}</span>` : ''}
                                                    </td>
                                                    <td class="px-4 py-2 text-sm text-gray-500">${finalScore.toFixed(1)}</td>
                                                    <td class="px-4 py-2 text-sm text-gray-500">${baseline.toFixed(1)}</td>
                                                    <td class="px-4 py-2 text-sm text-gray-500 ${gap > 0 ? 'text-green-600' : 'text-red-600'} font-medium">
                                                        ${gap > 0 ? '+' : ''}${gap.toFixed(1)}
                                                    </td>
                                                    <td class="px-4 py-2 text-sm font-medium ${priorityColor}">${priority}</td>
                                                </tr>
                                            `;
                                        }).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                tableHtml = '<div class="card p-6 text-center"><p class="text-gray-600">Aucun résultat disponible. Veuillez compléter toutes les étapes.</p></div>';
            }
            
            return tableHtml;
        }
        
        function renderVisualsTab() {
            return `
                <div class="grid grid-cols-1 gap-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold">Analyse des Écarts</h3>
                        </div>
                        <div class="p-4">
                            <div class="chart-container">
                                <canvas id="gapChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="text-lg font-semibold">Répartition des Priorités</h3>
                            </div>
                            <div class="p-4">
                                <div class="chart-container">
                                    <canvas id="priorityChart"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <h3 class="text-lg font-semibold">Top 10 des Écarts</h3>
                            </div>
                            <div class="p-4">
                                <div class="chart-container">
                                    <canvas id="topGapsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function renderHeatmapTab() {
            return `
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-lg font-semibold">Relations entre Objectifs</h3>
                    </div>
                    <div class="p-4">
                        <div class="mb-4">
                            <p class="text-gray-600">Cette carte thermique montre les relations entre les différents objectifs COBIT. Les couleurs représentent l'intensité des relations.</p>
                        </div>
                        <div class="overflow-auto">
                            <div id="heatmap" class="min-w-full"></div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Global chart instances for real-time updates
        let chartInstances = {
            radar: {},
            bar: {},
            gap: null,
            priority: null,
            domainRadar: null,
            topGaps: null,
            heatmap: null
        };

        function setupStepEventListeners() {
            if (appState.currentStep <= 10) {
                const dfKey = `DF${appState.currentStep}`;
                const dfData = appState.data[dfKey];

                dfData.inputs.forEach((value, index) => {
                    const inputId = `${dfKey}_input_${index}`;
                    const input = document.getElementById(inputId);

                    if (input) {
                        if (input.type === 'range') {
                            const valueDisplay = document.getElementById(`${inputId}_value`);
                            input.classList.add('input-glow');

                            input.addEventListener('input', (e) => {
                                const newValue = parseFloat(e.target.value);
                                appState.data[dfKey].inputs[index] = newValue;
                                valueDisplay.textContent = newValue;

                                // Visual feedback
                                showInputFeedback(input);

                                // Real-time updates
                                calculateResults(appState.currentStep);
                                updateAllChartsRealTime();
                                saveToLocalStorage();
                            });
                        } else if (input.type === 'checkbox') {
                            input.addEventListener('change', (e) => {
                                appState.data[dfKey].inputs[index] = e.target.checked ? 1 : 0;

                                // Visual feedback
                                showInputFeedback(input);

                                // Real-time updates
                                calculateResults(appState.currentStep);
                                updateAllChartsRealTime();
                                saveToLocalStorage();
                            });
                        } else if (input.tagName === 'SELECT') {
                            input.classList.add('input-glow');

                            input.addEventListener('change', (e) => {
                                appState.data[dfKey].inputs[index] = e.target.value ? parseInt(e.target.value) : null;

                                // Visual feedback
                                showInputFeedback(input);

                                // Real-time updates
                                calculateResults(appState.currentStep);
                                updateAllChartsRealTime();
                                saveToLocalStorage();
                            });
                        }
                    }
                });

                // Initial calculation
                calculateResults(appState.currentStep);

                // Force chart creation after a short delay
                setTimeout(() => {
                    console.log('Forcing chart creation for DF', appState.currentStep);
                    const dfKey = `DF${appState.currentStep}`;
                    const results = appState.results[dfKey];
                    if (results) {
                        createDFCharts(appState.currentStep, results.scores, results.baselines);
                    }
                }, 200);
            } else {
                // Canvas step - setup export listeners
                setupCanvasEventListeners();
                renderTabContent('summary');
                calculateFinalResults();

                // Force chart creation for canvas
                setTimeout(() => {
                    console.log('Forcing canvas chart creation');
                    createChartsWithData();
                }, 200);
            }
        }

        // New function for real-time chart updates
        function updateAllChartsRealTime() {
            // Recalculate final results
            calculateFinalResults();

            // Update current DF charts if visible
            if (appState.currentStep <= 10) {
                const dfKey = `DF${appState.currentStep}`;
                const results = appState.results[dfKey];
                if (results) {
                    updateDFChartsRealTime(appState.currentStep, results.scores, results.baselines);
                }
            }

            // Update canvas charts if on canvas step
            if (appState.currentStep > 10) {
                updateCanvasChartsRealTime();
            }
        }

        function updateDFChartsRealTime(dfNumber, scores, baselines) {
            // Update radar chart
            const radarChart = chartInstances.radar[`df${dfNumber}`];
            if (radarChart) {
                const domainData = calculateDomainAverages(scores, baselines);
                radarChart.data.datasets[0].data = domainData.avgData;
                radarChart.data.datasets[1].data = domainData.baselineData;
                radarChart.update('none'); // No animation for real-time updates
            }

            // Update bar chart
            const barChart = chartInstances.bar[`df${dfNumber}`];
            if (barChart) {
                const topObjectives = cobitData.objectives.slice(0, 10);
                const topScores = scores.slice(0, 10);
                const topBaselines = baselines.slice(0, 10);

                barChart.data.datasets[0].data = topScores;
                barChart.data.datasets[1].data = topBaselines;
                barChart.update('none');
            }
        }

        function updateCanvasChartsRealTime() {
            const finalResults = appState.finalResults;
            if (!finalResults || !finalResults.sortedByAbsGap) return;

            // Update gap chart
            if (chartInstances.gap) {
                const gaps = finalResults.sortedByAbsGap.slice(0, 15).map(r => r.gap);
                const labels = finalResults.sortedByAbsGap.slice(0, 15).map(r => r.objective);

                chartInstances.gap.data.labels = labels;
                chartInstances.gap.data.datasets[0].data = gaps;
                chartInstances.gap.data.datasets[0].backgroundColor = gaps.map(gap =>
                    gap > 0 ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)'
                );
                chartInstances.gap.data.datasets[0].borderColor = gaps.map(gap =>
                    gap > 0 ? 'rgba(16, 185, 129, 1)' : 'rgba(239, 68, 68, 1)'
                );
                chartInstances.gap.update('none');
            }

            // Update priority chart
            if (chartInstances.priority) {
                const priorityCounts = {
                    H: finalResults.sortedByAbsGap.filter(r => r.priority === 'H').length,
                    M: finalResults.sortedByAbsGap.filter(r => r.priority === 'M').length,
                    L: finalResults.sortedByAbsGap.filter(r => r.priority === 'L').length
                };

                chartInstances.priority.data.datasets[0].data = [priorityCounts.H, priorityCounts.M, priorityCounts.L];
                chartInstances.priority.update('none');
            }

            // Update domain radar chart
            if (chartInstances.domainRadar && finalResults.domainAverages) {
                const domainLabels = ['EDM', 'APO', 'BAI', 'DSS', 'MEA'];
                const domainAverages = domainLabels.map(label => finalResults.domainAverages[label] || 0);

                chartInstances.domainRadar.data.datasets[0].data = domainAverages;
                chartInstances.domainRadar.options.scales.r.suggestedMax = Math.max(...domainAverages) * 1.2;
                chartInstances.domainRadar.update('none');
            }

            // Update top gaps chart
            if (chartInstances.topGaps) {
                const top10 = finalResults.sortedByAbsGap.slice(0, 10);
                const labels = top10.map(r => r.objective);
                const gaps = top10.map(r => r.gap);

                chartInstances.topGaps.data.labels = labels;
                chartInstances.topGaps.data.datasets[0].data = gaps;
                chartInstances.topGaps.data.datasets[0].backgroundColor = gaps.map(gap =>
                    gap > 0 ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)'
                );
                chartInstances.topGaps.data.datasets[0].borderColor = gaps.map(gap =>
                    gap > 0 ? 'rgba(16, 185, 129, 1)' : 'rgba(239, 68, 68, 1)'
                );
                chartInstances.topGaps.update('none');
            }

            // Update statistics in real-time
            updateStatisticsRealTime();
        }

        function updateStatisticsRealTime() {
            const finalResults = appState.finalResults;
            if (!finalResults) return;

            // Update summary statistics
            const summaryStats = document.getElementById('summaryStats');
            if (summaryStats) {
                const totalObjectives = cobitData.objectives.length;
                const highPriority = finalResults.sortedByAbsGap.filter(r => r.priority === 'H').length;
                const mediumPriority = finalResults.sortedByAbsGap.filter(r => r.priority === 'M').length;
                const lowPriority = finalResults.sortedByAbsGap.filter(r => r.priority === 'L').length;

                const avgScore = finalResults.scores.reduce((a, b) => a + b, 0) / finalResults.scores.length;
                const avgBaseline = finalResults.baselines.reduce((a, b) => a + b, 0) / finalResults.baselines.length;
                const overallGap = avgScore - avgBaseline;

                summaryStats.innerHTML = `
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div class="bg-blue-50 p-4 rounded-lg transform transition-all duration-300 hover:scale-105">
                            <div class="text-2xl font-bold text-blue-600 animate-pulse">${totalObjectives}</div>
                            <div class="text-sm text-gray-600">Objectifs évalués</div>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg transform transition-all duration-300 hover:scale-105">
                            <div class="text-2xl font-bold text-red-600 ${highPriority > 0 ? 'animate-bounce' : ''}">${highPriority}</div>
                            <div class="text-sm text-gray-600">Haute priorité</div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg transform transition-all duration-300 hover:scale-105">
                            <div class="text-2xl font-bold text-yellow-600">${mediumPriority}</div>
                            <div class="text-sm text-gray-600">Moyenne priorité</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg transform transition-all duration-300 hover:scale-105">
                            <div class="text-2xl font-bold text-green-600">${lowPriority}</div>
                            <div class="text-sm text-gray-600">Basse priorité</div>
                        </div>
                    </div>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg transform transition-all duration-300 hover:scale-105">
                            <div class="text-xl font-bold text-gray-700">${avgScore.toFixed(2)}</div>
                            <div class="text-sm text-gray-600">Score moyen</div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg transform transition-all duration-300 hover:scale-105">
                            <div class="text-xl font-bold text-gray-700">${avgBaseline.toFixed(2)}</div>
                            <div class="text-sm text-gray-600">Baseline moyenne</div>
                        </div>
                        <div class="bg-${overallGap > 0 ? 'green' : 'red'}-50 p-4 rounded-lg transform transition-all duration-300 hover:scale-105">
                            <div class="text-xl font-bold text-${overallGap > 0 ? 'green' : 'red'}-600">
                                ${overallGap > 0 ? '+' : ''}${overallGap.toFixed(2)}
                            </div>
                            <div class="text-sm text-gray-600">Écart global</div>
                        </div>
                    </div>
                    <div class="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                        <div class="text-sm text-gray-600 mb-2">Mise à jour en temps réel</div>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-ping"></div>
                            <span class="text-xs text-gray-500">Les graphiques se mettent à jour automatiquement</span>
                        </div>
                    </div>
                `;
            }

            // Update real-time indicators
            addRealTimeIndicators();
        }

        function addRealTimeIndicators() {
            // Add visual feedback for real-time updates
            const indicators = document.querySelectorAll('.chart-container');
            indicators.forEach(container => {
                // Add a subtle glow effect during updates
                container.classList.add('updating');

                // Add real-time indicator if not present
                if (!container.querySelector('.real-time-indicator')) {
                    const indicator = document.createElement('div');
                    indicator.className = 'real-time-indicator';
                    indicator.textContent = 'LIVE';
                    container.appendChild(indicator);
                }

                setTimeout(() => {
                    container.classList.remove('updating');
                }, 500);
            });
        }

        function showInputFeedback(input) {
            // Add visual feedback to the input that was changed
            input.style.transform = 'scale(1.05)';
            input.style.boxShadow = '0 0 15px rgba(16, 185, 129, 0.5)';

            setTimeout(() => {
                input.style.transform = 'scale(1)';
                input.style.boxShadow = 'none';
            }, 300);

            // Show a brief "updating" message
            const parent = input.closest('.mb-6') || input.parentElement;
            if (parent && !parent.querySelector('.update-indicator')) {
                const updateIndicator = document.createElement('div');
                updateIndicator.className = 'update-indicator text-xs text-green-600 font-medium mt-1';
                updateIndicator.innerHTML = '<i class="fas fa-sync-alt animate-spin mr-1"></i>Mise à jour des graphiques...';
                parent.appendChild(updateIndicator);

                setTimeout(() => {
                    updateIndicator.remove();
                }, 1000);
            }
        }

        function calculateDomainAverages(scores, baselines) {
            console.log('Calculating domain averages with scores:', scores, 'baselines:', baselines);

            const domainAverages = {};
            const domainBaselines = {};
            const domainCounts = {};

            // Initialize domain data
            for (const domain in cobitData.domains) {
                domainAverages[domain] = 0;
                domainBaselines[domain] = 0;
                domainCounts[domain] = 0;
            }

            // Calculate domain averages
            cobitData.objectives.forEach((objective, index) => {
                const domain = getDomain(objective);
                if (domain) {
                    domainAverages[domain] += scores[index] || 0;
                    domainBaselines[domain] += baselines[index] || 0;
                    domainCounts[domain]++;
                }
            });

            // Calculate final averages
            const labels = [];
            const avgData = [];
            const baselineData = [];

            for (const domain in cobitData.domains) {
                if (domainCounts[domain] > 0) {
                    labels.push(domain);
                    avgData.push(domainAverages[domain] / domainCounts[domain]);
                    baselineData.push(domainBaselines[domain] / domainCounts[domain]);
                }
            }

            console.log('Domain calculation result:', { labels, avgData, baselineData });
            return { labels, avgData, baselineData };
        }
        
        function setupTabEventListeners() {
            // Tab switching
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    renderTabContent(btn.dataset.tab);
                });
            });
        }

        function setupCanvasEventListeners() {
            const exportPdfBtn = document.getElementById('exportPdfBtn');
            const exportJsonBtn = document.getElementById('exportJsonBtn');
            const printBtn = document.getElementById('printBtn');
            
            if (exportPdfBtn) {
                exportPdfBtn.addEventListener('click', exportToPDF);
            }
            
            if (exportJsonBtn) {
                exportJsonBtn.addEventListener('click', exportToJSON);
            }
            
            if (printBtn) {
                printBtn.addEventListener('click', () => window.print());
            }
        }

        function calculateResults(dfNumber) {
            const dfKey = `DF${dfNumber}`;
            const inputs = appState.data[dfKey].inputs;

            console.log('Calculating results for', dfKey, 'with inputs:', inputs);

            // Calculate scores using matrix multiplication
            const scores = calculateScores(dfNumber, inputs);
            const baselines = calculateBaseline(dfNumber);

            console.log('Calculated scores:', scores);
            console.log('Calculated baselines:', baselines);

            // Store results for final calculation
            appState.results[dfKey] = {
                scores: scores,
                baselines: baselines
            };
            
            const resultsTableBody = document.getElementById('resultsTableBody');
            if (resultsTableBody) {
                let html = '';
                
                for (let i = 0; i < Math.min(scores.length, cobitData.objectives.length); i++) {
                    const score = scores[i] || 0;
                    const baseline = baselines[i] || 1;
                    const ri = calculateRI(score, baseline, appState.dfWeights[dfNumber-1]);
                    const domain = getDomain(cobitData.objectives[i]);
                    
                    html += `
                        <tr>
                            <td class="px-4 py-2 text-sm font-medium text-gray-900">
                                ${cobitData.objectives[i]}
                                ${domain ? `<span class="domain-badge domain-${domain.toLowerCase()}">${domain}</span>` : ''}
                            </td>
                            <td class="px-4 py-2 text-sm text-gray-500">${score.toFixed(2)}</td>
                            <td class="px-4 py-2 text-sm text-gray-500">${baseline.toFixed(2)}</td>
                            <td class="px-4 py-2 text-sm text-gray-500 font-medium ${ri > 0 ? 'text-green-600' : ri < 0 ? 'text-red-600' : ''}">
                                ${ri > 0 ? '+' : ''}${ri}%
                            </td>
                        </tr>
                    `;
                }
                
                resultsTableBody.innerHTML = html;
            }
            
            // Create charts for this DF
            console.log('About to create charts for DF', dfNumber);
            createDFCharts(dfNumber, scores, baselines);
        }
        
        function createDFCharts(dfNumber, scores, baselines) {
            console.log('Creating charts for DF', dfNumber, 'with scores:', scores.length, 'baselines:', baselines.length);

            // Destroy existing charts to prevent memory leaks
            if (chartInstances.radar[`df${dfNumber}`]) {
                chartInstances.radar[`df${dfNumber}`].destroy();
            }
            if (chartInstances.bar[`df${dfNumber}`]) {
                chartInstances.bar[`df${dfNumber}`].destroy();
            }

            // Create charts immediately - DOM should be ready
            createRadarChart(dfNumber, scores, baselines);
            createBarChart(dfNumber, scores, baselines);
        }

        function createRadarChart(dfNumber, scores, baselines) {
            const radarCtx = document.getElementById(`df${dfNumber}_radarChart`);
            console.log('Looking for radar chart element:', `df${dfNumber}_radarChart`, radarCtx);

            if (radarCtx) {
                console.log('Creating radar chart for DF', dfNumber);
                const domainData = calculateDomainAverages(scores, baselines);
                console.log('Domain data:', domainData);

                if (domainData.labels.length > 0) {
                    try {
                        chartInstances.radar[`df${dfNumber}`] = new Chart(radarCtx, {
                    type: 'radar',
                    data: {
                        labels: domainData.labels,
                        datasets: [
                            {
                                label: 'Vos scores',
                                data: domainData.avgData,
                                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: 'rgba(59, 130, 246, 1)'
                            },
                            {
                                label: 'Baseline',
                                data: domainData.baselineData,
                                backgroundColor: 'rgba(107, 114, 128, 0.2)',
                                borderColor: 'rgba(107, 114, 128, 1)',
                                pointBackgroundColor: 'rgba(107, 114, 128, 1)',
                                pointBorderColor: '#fff',
                                pointHoverBackgroundColor: '#fff',
                                pointHoverBorderColor: 'rgba(107, 114, 128, 1)'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Performance par Domaine'
                            },
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            r: {
                                min: 0,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        },
                        animation: {
                            duration: 300 // Faster animation for real-time updates
                        }
                    }
                });
                        console.log('Radar chart created successfully for DF', dfNumber);
                    } catch (error) {
                        console.error('Error creating radar chart for DF', dfNumber, ':', error);
                    }
                } else {
                    console.log('Radar chart element not found for DF', dfNumber);
                }
            } else {
                console.log('Domain data is empty for DF', dfNumber);
            }
        }

        function createBarChart(dfNumber, scores, baselines) {
            const barCtx = document.getElementById(`df${dfNumber}_barChart`);
            console.log('Looking for bar chart element:', `df${dfNumber}_barChart`, barCtx);

            if (barCtx) {
                const topObjectives = cobitData.objectives.slice(0, 10);
                const topScores = scores.slice(0, 10);
                const topBaselines = baselines.slice(0, 10);

                try {
                    chartInstances.bar[`df${dfNumber}`] = new Chart(barCtx, {
                    type: 'bar',
                    data: {
                        labels: topObjectives,
                        datasets: [
                            {
                                label: 'Vos scores',
                                data: topScores,
                                backgroundColor: 'rgba(59, 130, 246, 0.7)',
                                borderColor: 'rgba(59, 130, 246, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'Baseline',
                                data: topBaselines,
                                backgroundColor: 'rgba(107, 114, 128, 0.7)',
                                borderColor: 'rgba(107, 114, 128, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Comparaison des Objectifs'
                            },
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Score'
                                }
                            }
                        },
                        animation: {
                            duration: 300 // Faster animation for real-time updates
                        }
                    }
                });
                    console.log('Bar chart created successfully for DF', dfNumber);
                } catch (error) {
                    console.error('Error creating bar chart for DF', dfNumber, ':', error);
                }
            } else {
                console.log('Bar chart element not found for DF', dfNumber);
            }
        }

        function calculateFinalResults() {
            // Calculate cumulative scores for all objectives across all DFs
            const finalScores = new Array(cobitData.objectives.length).fill(0);
            const finalBaselines = new Array(cobitData.objectives.length).fill(0);
            
            // Sum scores from all DFs
            for (let dfNum = 1; dfNum <= 10; dfNum++) {
                const dfKey = `DF${dfNum}`;
                const results = appState.results[dfKey];
                
                if (results) {
                    for (let i = 0; i < finalScores.length; i++) {
                        finalScores[i] += results.scores[i] || 0;
                        finalBaselines[i] += results.baselines[i] || 0;
                    }
                }
            }
            
            // Calculate domain averages
            const domains = {
                EDM: { objectives: cobitData.domains.EDM, sum: 0, count: 0 },
                APO: { objectives: cobitData.domains.APO, sum: 0, count: 0 },
                BAI: { objectives: cobitData.domains.BAI, sum: 0, count: 0 },
                DSS: { objectives: cobitData.domains.DSS, sum: 0, count: 0 },
                MEA: { objectives: cobitData.domains.MEA, sum: 0, count: 0 }
            };

            // Parcourir chaque objectif
            cobitData.objectives.forEach((objective, index) => {
                const domain = Object.keys(domains).find(d => domains[d].objectives.includes(objective));
                if (domain) {
                    domains[domain].sum += finalScores[index];
                    domains[domain].count++;
                }
            });

            const domainAverages = {};
            for (const domain in domains) {
                domainAverages[domain] = domains[domain].count > 0 ? domains[domain].sum / domains[domain].count : 0;
            }
            
            // Store final results with gaps and priorities
            const finalResultsWithGap = [];
            cobitData.objectives.forEach((objective, index) => {
                const finalScore = finalScores[index] || 0;
                const baseline = finalBaselines[index] || 0;
                const gap = finalScore - baseline;
                const priority = Math.abs(gap) > baseline * 0.5 ? 'H' : 
                                Math.abs(gap) > baseline * 0.2 ? 'M' : 'L';
                
                finalResultsWithGap.push({
                    objective,
                    finalScore,
                    baseline,
                    gap,
                    priority
                });
            });
            
            // Sort by absolute gap descending
            finalResultsWithGap.sort((a, b) => Math.abs(b.gap) - Math.abs(a.gap));
            
            // Store final results
            appState.finalResults = {
                scores: finalScores,
                baselines: finalBaselines,
                domainAverages: domainAverages,
                sortedByAbsGap: finalResultsWithGap
            };
            
            // Update visuals if on the visuals tab
            if (document.querySelector('.tab-btn.active')?.dataset.tab === 'visuals' ||
                document.querySelector('.tab-btn.active')?.dataset.tab === 'heatmap') {
                createChartsWithData();
            }
        }
        
        function createChartsWithData() {
            const finalResults = appState.finalResults;
            if (!finalResults || !finalResults.sortedByAbsGap) return;

            // Add timeout to ensure DOM is ready
            setTimeout(() => {
                // Destroy existing charts to prevent memory leaks
                if (chartInstances.gap) {
                    chartInstances.gap.destroy();
                }
                if (chartInstances.priority) {
                    chartInstances.priority.destroy();
                }
                if (chartInstances.domainRadar) {
                    chartInstances.domainRadar.destroy();
                }
                if (chartInstances.topGaps) {
                    chartInstances.topGaps.destroy();
                }

            // Gap Chart
            const gapCtx = document.getElementById('gapChart');
            if (gapCtx) {
                const gaps = finalResults.sortedByAbsGap.slice(0, 15).map(r => r.gap);
                const labels = finalResults.sortedByAbsGap.slice(0, 15).map(r => r.objective);

                chartInstances.gap = new Chart(gapCtx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Écart (Score - Baseline)',
                            data: gaps,
                            backgroundColor: gaps.map(gap => 
                                gap > 0 ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)'
                            ),
                            borderColor: gaps.map(gap => 
                                gap > 0 ? 'rgba(16, 185, 129, 1)' : 'rgba(239, 68, 68, 1)'
                            ),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Écarts par Objectif COBIT'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `Écart: ${context.parsed.y > 0 ? '+' : ''}${context.parsed.y.toFixed(1)}`;
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Écart'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Objectifs COBIT'
                                }
                            }
                        },
                        animation: {
                            duration: 300 // Faster animation for real-time updates
                        }
                    }
                });
            }

            // Priority Chart
            const priorityCtx = document.getElementById('priorityChart');
            if (priorityCtx) {
                const priorityCounts = {
                    H: finalResults.sortedByAbsGap.filter(r => r.priority === 'H').length,
                    M: finalResults.sortedByAbsGap.filter(r => r.priority === 'M').length,
                    L: finalResults.sortedByAbsGap.filter(r => r.priority === 'L').length
                };

                chartInstances.priority = new Chart(priorityCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Haute Priorité', 'Moyenne Priorité', 'Basse Priorité'],
                        datasets: [{
                            data: [priorityCounts.H, priorityCounts.M, priorityCounts.L],
                            backgroundColor: [
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(16, 185, 129, 0.8)'
                            ],
                            borderColor: [
                                'rgba(239, 68, 68, 1)',
                                'rgba(245, 158, 11, 1)',
                                'rgba(16, 185, 129, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Répartition des Priorités'
                            },
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `${context.label}: ${context.parsed} objectifs`;
                                    }
                                }
                            }
                        },
                        animation: {
                            duration: 300 // Faster animation for real-time updates
                        }
                    }
                });
            }

            // Domain Radar Chart
            const domainRadarCtx = document.getElementById('domainRadarChart');
            if (domainRadarCtx && finalResults.domainAverages) {
                const domainLabels = ['EDM', 'APO', 'BAI', 'DSS', 'MEA'];
                const domainAverages = domainLabels.map(label => finalResults.domainAverages[label] || 0);

                chartInstances.domainRadar = new Chart(domainRadarCtx, {
                    type: 'radar',
                    data: {
                        labels: domainLabels,
                        datasets: [{
                            label: 'Score moyen',
                            data: domainAverages,
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1,
                            pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                            pointBorderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Performance par Domaine'
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                suggestedMin: 0,
                                suggestedMax: Math.max(...domainAverages) * 1.2
                            }
                        },
                        animation: {
                            duration: 300 // Faster animation for real-time updates
                        }
                    }
                });
            }

            // Top Gaps Horizontal Bar Chart
            const topGapsCtx = document.getElementById('topGapsChart');
            if (topGapsCtx) {
                const top10 = finalResults.sortedByAbsGap.slice(0, 10);
                const labels = top10.map(r => r.objective);
                const gaps = top10.map(r => r.gap);

                chartInstances.topGaps = new Chart(topGapsCtx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Écart',
                            data: gaps,
                            backgroundColor: gaps.map(gap => 
                                gap > 0 ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)'
                            ),
                            borderColor: gaps.map(gap => 
                                gap > 0 ? 'rgba(16, 185, 129, 1)' : 'rgba(239, 68, 68, 1)'
                            ),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Top 10 des Écarts'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return `Écart: ${context.parsed.x > 0 ? '+' : ''}${context.parsed.x.toFixed(1)}`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Écart'
                                }
                            }
                        },
                        animation: {
                            duration: 300 // Faster animation for real-time updates
                        }
                    }
                });
            }
            
            // Heatmap
            const heatmapEl = document.getElementById('heatmap');
            if (heatmapEl) {
                // Generate a simple heatmap of relationships
                heatmapEl.innerHTML = '';
                
                // Header row
                let headerRow = '<div class="flex">';
                headerRow += '<div class="w-24"></div>'; // Empty cell for top-left corner
                for (let i = 0; i < 10; i++) {
                    headerRow += `<div class="w-8 text-center text-xs font-medium">${cobitData.objectives[i]}</div>`;
                }
                headerRow += '</div>';
                heatmapEl.innerHTML = headerRow;
                
                // Data rows
                for (let i = 0; i < 10; i++) {
                    let row = `<div class="flex items-center">`;
                    row += `<div class="w-24 text-xs font-medium py-1">${cobitData.objectives[i]}</div>`;
                    
                    for (let j = 0; j < 10; j++) {
                        // Simulate relationship intensity (0-100)
                        const intensity = Math.floor(Math.random() * 101);
                        let bgColor = '';
                        
                        if (intensity > 80) bgColor = 'bg-red-600';
                        else if (intensity > 60) bgColor = 'bg-orange-500';
                        else if (intensity > 40) bgColor = 'bg-yellow-400';
                        else if (intensity > 20) bgColor = 'bg-green-400';
                        else bgColor = 'bg-blue-400';
                        
                        row += `<div class="heatmap-cell ${bgColor}" title="Relation entre ${cobitData.objectives[i]} et ${cobitData.objectives[j]}: ${intensity}%">${intensity}</div>`;
                    }
                    
                    row += '</div>';
                    heatmapEl.innerHTML += row;
                }
            }
            }, 100); // End of setTimeout
        }

        function exportToPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Title
            doc.setFontSize(20);
            doc.text('COBIT 2019 - Rapport d\'Évaluation', 20, 30);
            
            // Date
            doc.setFontSize(12);
            doc.text(`Date: ${new Date().toLocaleDateString('fr-FR')}`, 20, 45);
            
            let yPosition = 60;
            
            // Summary section
            doc.setFontSize(16);
            doc.text('Résumé Exécutif', 20, yPosition);
            yPosition += 15;
            
            doc.setFontSize(12);
            const finalResults = appState.finalResults;
            if (finalResults) {
                const highPriority = finalResults.sortedByAbsGap.filter(r => r.priority === 'H').length;
                const mediumPriority = finalResults.sortedByAbsGap.filter(r => r.priority === 'M').length;
                const lowPriority = finalResults.sortedByAbsGap.filter(r => r.priority === 'L').length;
                
                doc.text(`Objectifs haute priorité: ${highPriority}`, 20, yPosition);
                yPosition += 10;
                doc.text(`Objectifs moyenne priorité: ${mediumPriority}`, 20, yPosition);
                yPosition += 10;
                doc.text(`Objectifs basse priorité: ${lowPriority}`, 20, yPosition);
                yPosition += 20;
                
                // Top objectives
                doc.setFontSize(14);
                doc.text('Objectifs Prioritaires:', 20, yPosition);
                yPosition += 10;
                
                doc.setFontSize(12);
                finalResults.sortedByAbsGap.slice(0, 5).forEach((obj, index) => {
                    doc.text(`${index+1}. ${obj.objective} - Écart: ${obj.gap > 0 ? '+' : ''}${obj.gap.toFixed(1)} (${obj.priority === 'H' ? 'Haute' : obj.priority === 'M' ? 'Moyenne' : 'Basse'} priorité)`, 25, yPosition);
                    yPosition += 10;
                });
            }
            
            yPosition += 10;
            
            // Design Factors section
            doc.setFontSize(16);
            doc.text('Configuration des Design Factors', 20, yPosition);
            yPosition += 15;
            
            doc.setFontSize(12);
            for (let i = 1; i <= 10; i++) {
                const dfKey = `DF${i}`;
                const dfData = appState.data[dfKey];
                const config = cobitData.inputConfigs[dfKey];
                
                if (yPosition > 250) {
                    doc.addPage();
                    yPosition = 30;
                }
                
                doc.text(`DF${i} - ${getDFTitle(i)}:`, 20, yPosition);
                yPosition += 10;
                
                dfData.inputs.forEach((input, index) => {
                    if (input !== null && input !== undefined) {
                        const label = dfData.labels[index];
                        doc.text(`  ${label}: ${input}`, 25, yPosition);
                        yPosition += 8;
                    }
                });
                yPosition += 5;
            }
            
            // Save the PDF
            doc.save('cobit-2019-evaluation.pdf');
        }
        
        function exportToJSON() {
            const exportData = {
                timestamp: new Date().toISOString(),
                version: '1.0',
                data: appState.data,
                results: appState.results,
                finalResults: appState.finalResults
            };
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `cobit-2019-evaluation-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }
        
        function importFromJSON() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (event) => {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const importedData = JSON.parse(e.target.result);
                            
                            // Validate the imported data structure
                            if (importedData.data && typeof importedData.data === 'object') {
                                // Merge imported data with current state
                                Object.assign(appState.data, importedData.data);
                                if (importedData.results) {
                                    appState.results = importedData.results;
                                }
                                if (importedData.finalResults) {
                                    appState.finalResults = importedData.finalResults;
                                }
                                
                                // Refresh the current view
                                renderCurrentStep();
                                saveToLocalStorage();
                                
                                alert('Données importées avec succès !');
                            } else {
                                alert('Format de fichier invalide. Veuillez sélectionner un fichier JSON valide.');
                            }
                        } catch (error) {
                            alert('Erreur lors de l\'importation du fichier. Veuillez vérifier le format.');
                            console.error('Import error:', error);
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }
        
        function showHelp(dfNumber) {
            const helpTexts = {
                1: 'Enterprise Strategy définit l\'orientation stratégique de votre organisation. Évaluez l\'importance relative de chaque archétype stratégique.',
                2: 'Enterprise Goals représentent les objectifs métier de votre organisation. Sélectionnez le niveau d\'importance pour chaque objectif.',
                3: 'Risk Profile évalue votre profil de risque IT. Ajustez les curseurs selon votre tolérance au risque dans chaque domaine.',
                4: 'IT-Related Issues identifie les problématiques IT spécifiques à votre organisation.',
                5: 'Threat Landscape évalue les menaces de sécurité auxquelles votre organisation fait face.',
                6: 'Compliance Requirements définit vos exigences de conformité réglementaire et sectorielle.',
                7: 'Role of IT détermine le rôle stratégique de l\'IT dans votre organisation.',
                8: 'Sourcing Model définit votre approche de sourcing IT (interne vs externe).',
                9: 'IT Implementation Methods évalue vos méthodes de mise en œuvre IT.',
                10: 'Enterprise Size reflète la taille et la complexité de votre organisation.'
            };
            
            const helpText = helpTexts[dfNumber] || 'Aide non disponible pour cette section.';
            
            // Create a nicer alert
            const alertDiv = document.createElement('div');
            alertDiv.className = 'fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50';
            alertDiv.innerHTML = `
                <div class="bg-white rounded-xl shadow-lg p-6 max-w-md w-full">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-blue-800">Aide - DF${dfNumber}</h3>
                        <button class="text-gray-500 hover:text-gray-700" onclick="this.parentElement.parentElement.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <p class="text-gray-700 mb-4">${helpText}</p>
                    <div class="text-right">
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg" onclick="this.parentElement.parentElement.parentElement.remove()">
                            Fermer
                        </button>
                    </div>
                </div>
            `;
            
            document.body.appendChild(alertDiv);
        }
        
        function resetAllData() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser toutes les données ? Cette action est irréversible.')) {
                // Reset to initial state
                initializeAppState();
                appState.currentStep = 1;
                appState.results = {};
                appState.finalResults = {};
                
                // Clear localStorage
                localStorage.removeItem('cobit2019Data');
                
                // Reset UI
                updateProgressBar();
                renderCurrentStep();
                
                alert('Toutes les données ont été réinitialisées.');
            }
        }
        
        function saveToLocalStorage() {
            localStorage.setItem('cobit2019Data', JSON.stringify(appState));
        }

        function loadFromLocalStorage() {
            const saved = localStorage.getItem('cobit2019Data');
            if (saved) {
                const savedState = JSON.parse(saved);
                Object.assign(appState, savedState);
                updateProgressBar();
            }
        }

        // Initialize the application when the page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>